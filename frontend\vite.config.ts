import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react()],
  server: {
    port: 3000,
    host: '0.0.0.0', // 强制使用IPv4
    proxy: {
      '^/api/.*': {
        target: 'http://0.0.0.0:8888',  // 强制使用IPv4
        changeOrigin: true,
        secure: false,
        rewrite: (path) => path,
      },
    },
  },
})
