import React from 'react'
import { Menu } from 'antd'
import { useNavigate, useLocation } from 'react-router-dom'
import { menuItems } from '../config/menuItems'

const TopNavbar: React.FC = () => {
  const navigate = useNavigate()
  const location = useLocation()

  return (
    <div style={{
      display: 'flex',
      alignItems: 'center',
      height: '48px',
      padding: '0 12px'
    }}>
      <div style={{
        color: 'white',
        fontSize: '16px',
        fontWeight: 'bold',
        marginRight: '16px'
      }}>
        KeyHub
      </div>

      <Menu
        theme="dark"
        mode="horizontal"
        selectedKeys={[location.pathname]}
        items={menuItems}
        onClick={({ key }) => navigate(key)}
        style={{
          flex: 1,
          border: 'none',
          background: 'transparent',
          lineHeight: '48px'
        }}
      />
    </div>
  )
}

export default TopNavbar
