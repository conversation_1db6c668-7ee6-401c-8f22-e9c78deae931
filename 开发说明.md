# KeyHub 开发说明

## 环境要求

- Go 1.24+
- Node.js 18+
- npm 或 yarn

## 快速开始

### 1. 自动启动（推荐）
双击运行 `start-dev.bat` 脚本，它会：
- 检查环境
- 安装依赖
- 创建配置文件
- 启动前后端服务

### 2. 手动启动

#### 安装依赖
```bash
# 安装Go依赖
go mod tidy

# 安装前端依赖
cd frontend
npm install
cd ..
```

#### 创建配置文件
```bash
copy .env.example .env
```

#### 启动后端服务
```bash
go run cmd/server/main.go
```

#### 启动前端服务（新终端）
```bash
cd frontend
npm run dev
```

## 访问地址

- 后端API: http://localhost:8888
- 前端界面: http://localhost:3000
- API文档: http://localhost:8888/health (健康检查)

## 项目结构

```
KeyHub/
├── cmd/server/          # 应用入口
├── internal/            # 内部包
│   ├── api/            # API路由和处理器
│   ├── config/         # 配置管理
│   ├── database/       # 数据库操作
│   ├── models/         # 数据模型
│   └── services/       # 业务逻辑
├── frontend/           # React前端
│   ├── src/
│   │   ├── components/ # 组件
│   │   ├── pages/      # 页面
│   │   ├── services/   # API调用
│   │   └── types/      # 类型定义
│   └── package.json
├── go.mod              # Go模块文件
└── start-dev.bat       # 开发启动脚本
```

## 开发命令

```bash
# 使用Makefile（如果支持）
make help              # 查看所有命令
make install-deps      # 安装依赖
make dev-backend       # 启动后端
make dev-frontend      # 启动前端
make build            # 构建项目
make clean            # 清理构建文件
```

## 功能模块

### 已实现功能
- ✅ 项目结构搭建
- ✅ 数据库模型定义
- ✅ RESTful API框架
- ✅ React前端框架
- ✅ 路由系统
- ✅ API Key完整CRUD操作
- ✅ API Key加密存储
- ✅ 余额查询基础功能
- ✅ 前端管理界面

### 待实现功能
- [ ] 多线程测试引擎
- [ ] 实时状态监控
- [ ] 数据可视化
- [ ] 配置管理系统
- [ ] 日志和监控

## API测试

### 使用测试脚本
运行 `test-api.bat` 可以快速测试API接口功能。

### 手动测试API
```bash
# 健康检查
curl http://localhost:8888/health

# 获取所有API Key
curl http://localhost:8888/api/v1/apikeys

# 创建API Key
curl -X POST http://localhost:8888/api/v1/apikeys \
  -H "Content-Type: application/json" \
  -d '{"name":"测试Key","provider":"openai","api_key":"sk-test123","description":"测试"}'

# 更新API Key
curl -X PUT http://localhost:8888/api/v1/apikeys/1 \
  -H "Content-Type: application/json" \
  -d '{"name":"更新后的名称"}'

# 删除API Key
curl -X DELETE http://localhost:8888/api/v1/apikeys/1
```

## 功能特点

### API Key管理
- 📝 **完整CRUD**: 支持创建、读取、更新、删除操作
- 🏷️ **多提供商**: 支持2个主要AI服务提供商
- ✅ **参数验证**: 完善的输入验证和错误处理
- 🎨 **友好界面**: 现代化的React管理界面
- 🔄 **批量操作**: 支持批量创建、测试和删除

### 余额查询
- 💰 **三种余额**: 总余额(API返回)、充值余额、赠送余额
- 📊 **历史记录**: 余额变化历史追踪
- 🔌 **真实API**: 支持真实的余额查询API调用
- 🎯 **智能适配**: 根据提供商配置自动适配不同的API格式
- ✅ **准确数据**: 直接使用API返回的余额数据，不进行计算
- 🏗️ **模块化架构**: 每个提供商的解析逻辑独立文件，易于维护和扩展

## 提供商配置

### 配置文件说明
项目使用Go代码定义AI服务提供商信息，提供类型安全和易于维护的配置方式。

### 添加新提供商
1. 编辑 `internal/config/custom_providers.go` 文件
2. 在 `GetCustomProviders()` 函数中添加新的提供商配置：

```go
{
    Name:            "自定义AI服务",
    Code:            "custom-ai",
    Color:           "blue",
    Currency:        "USD", // 支持: USD、CNY、TOKEN
    APIEndpoint:     "https://api.custom-ai.com",
    Description:     "自定义AI服务提供商",
    SupportBalance:  true,
    BalanceEndpoint: "/v1/balance",
    SupportChat:     true,
    ChatEndpoint:    "/v1/chat/completions",
    ChatModel:       "custom-model",
}
```

3. 如果支持余额查询，需要添加余额解析器：
   - 在 `internal/services/balance_parser.go` 的 `GetBalanceParser` 函数中添加新的 case
4. 重新编译并启动服务即可生效

### 默认支持的提供商

#### 主要AI服务商
- **OpenAI** - GPT系列模型，支持聊天测试
- **SiliconFlow** - AI服务平台，支持聊天测试和余额查询

### 架构优势
- 🔧 **类型安全**: 使用Go结构体定义，编译时检查
- 📝 **易于维护**: 代码化配置，支持IDE智能提示
- 🔄 **热更新**: 修改配置后重新编译即可生效
- 🎯 **分离关注**: 内置提供商与自定义提供商分离

## 注意事项

1. 确保Go和Node.js环境已正确安装
2. 首次运行需要下载依赖，可能需要一些时间
3. 数据库文件会自动创建在项目根目录
4. 修改代码后，前端会自动热重载，后端需要重启
5. API Key会被加密存储，确保数据安全
