package config

// ProviderInfo 提供商信息
type ProviderInfo struct {
	Name            string `json:"name"`             // 显示名称
	Code            string `json:"code"`             // 代码标识
	Color           string `json:"color"`            // 前端显示颜色
	Currency        string `json:"currency"`         // 币种：USD、CNY、TOKEN
	APIEndpoint     string `json:"api_endpoint"`     // API端点
	Description     string `json:"description"`      // 描述
	SupportBalance  bool   `json:"support_balance"`  // 是否支持余额查询
	BalanceEndpoint string `json:"balance_endpoint"` // 余额查询端点（如果支持）
	SupportChat     bool   `json:"support_chat"`     // 是否支持聊天测试
	ChatEndpoint    string `json:"chat_endpoint"`    // 聊天API端点
	ChatModel       string `json:"chat_model"`       // 默认测试模型
}

// GetSupportedProviders 获取支持的提供商列表
func GetSupportedProviders() []ProviderInfo {
	return []ProviderInfo{
		{
			Name:           "OpenAI",
			Code:           "openai",
			Color:          "green",
			Currency:       "USD",
			APIEndpoint:    "https://api.openai.com",
			Description:    "OpenAI GPT系列模型",
			SupportBalance: false,
			SupportChat:    true,
			ChatEndpoint:   "/v1/chat/completions",
			ChatModel:      "gpt-3.5-turbo",
		},
		{
			Name:            "SiliconFlow",
			Code:            "siliconflow",
			Color:           "purple",
			Currency:        "CNY",
			APIEndpoint:     "https://api.siliconflow.cn",
			Description:     "SiliconFlow AI服务平台",
			SupportBalance:  true,
			BalanceEndpoint: "/v1/user/info",
			SupportChat:     true,
			ChatEndpoint:    "/v1/chat/completions",
			ChatModel:       "Qwen/Qwen2.5-7B-Instruct",
		},
	}
}

// GetProviderByCode 根据代码获取提供商信息
func GetProviderByCode(code string) *ProviderInfo {
	providers := GetAllProviders()
	for _, provider := range providers {
		if provider.Code == code {
			return &provider
		}
	}
	return nil
}
