@echo off
chcp 65001 >nul
echo ========================================
echo SiliconFlow余额查询功能测试
echo ========================================
echo.

echo 1. 创建SiliconFlow API Key测试数据...
curl.exe -X POST http://localhost:8888/api/v1/apikeys ^
  -H "Content-Type: application/json" ^
  -d "{\"name\":\"SiliconFlow测试Key\",\"provider\":\"siliconflow\",\"api_key\":\"sk-test123456789\",\"description\":\"用于测试余额查询功能\"}"

echo.
echo.

echo 2. 获取所有API Key，查看创建的测试数据...
curl.exe -s http://localhost:8888/api/v1/apikeys

echo.
echo.

echo 3. 测试余额查询功能（使用ID=1，如果不存在请手动调整）...
echo 注意：这会调用真实的SiliconFlow API，请确保API Key有效
curl.exe -X POST http://localhost:8888/api/v1/balance ^
  -H "Content-Type: application/json" ^
  -d "{\"api_key_id\":1}"

echo.
echo.

echo 4. 查看余额历史记录...
curl.exe -s http://localhost:8888/api/v1/balance/history/1

echo.
echo.
echo 测试完成！
echo 如果看到错误，请检查：
echo 1. 后端服务是否正在运行 (http://localhost:8888)
echo 2. API Key是否有效
echo 3. 网络连接是否正常
pause
