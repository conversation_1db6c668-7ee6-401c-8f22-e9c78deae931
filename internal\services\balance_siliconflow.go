package services

import (
	"encoding/json"
	"fmt"
	"strconv"
)

// SiliconFlowBalanceParser SiliconFlow余额解析器
type SiliconFlowBalanceParser struct{}

// SiliconFlowResponse SiliconFlow API响应结构
type SiliconFlowResponse struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
	Status  bool   `json:"status"`
	Data    struct {
		Balance       string `json:"balance"`       // 赠送余额
		ChargeBalance string `json:"chargeBalance"` // 充值余额
		TotalBalance  string `json:"totalBalance"`  // 总余额
	} `json:"data"`
}

// ParseBalance 解析SiliconFlow余额响应
func (p *SiliconFlowBalanceParser) ParseBalance(responseBody []byte) (*BalanceData, error) {
	var response SiliconFlowResponse

	if err := json.Unmarshal(responseBody, &response); err != nil {
		return nil, fmt.Errorf("解析SiliconFlow响应失败: %v", err)
	}

	// 检查API响应状态
	if response.Code != 20000 || !response.Status {
		return nil, fmt.Errorf("SiliconFlow API返回错误: %s", response.Message)
	}

	// 将字符串转换为浮点数
	var totalBalance, chargeBalance, grantBalance float64
	var err error

	if totalBalance, err = parseFloat(response.Data.TotalBalance); err != nil {
		return nil, fmt.Errorf("解析总余额失败: %v", err)
	}

	if chargeBalance, err = parseFloat(response.Data.ChargeBalance); err != nil {
		return nil, fmt.Errorf("解析充值余额失败: %v", err)
	}

	if grantBalance, err = parseFloat(response.Data.Balance); err != nil {
		return nil, fmt.Errorf("解析赠送余额失败: %v", err)
	}

	return &BalanceData{
		TotalBalance: totalBalance,   // 总余额 (totalBalance字段)
		GrantBalance: grantBalance,   // 赠送余额 (balance字段)
		PaidBalance:  chargeBalance,  // 充值余额 (chargeBalance字段)
	}, nil
}

// parseFloat 辅助函数：将字符串转换为浮点数
func parseFloat(s string) (float64, error) {
	if s == "" {
		return 0.0, nil
	}
	return strconv.ParseFloat(s, 64)
}
