package services

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"
	"time"

	"keyhub/internal/config"
)

// ChatTestRequest 聊天测试请求结构
type ChatTestRequest struct {
	APIKeyID uint   `json:"api_key_id"`
	TestType string `json:"test_type"` // chat
	Model    string `json:"model"`     // 可选，使用指定模型
}

// ChatTestResult 聊天测试结果
type ChatTestResult struct {
	Success      bool   `json:"success"`
	ResponseTime int    `json:"response_time"` // 毫秒
	ErrorMessage string `json:"error_message"`
	Response     string `json:"response"`      // AI回复内容
	Model        string `json:"model"`         // 使用的模型
}

// ChatTester 聊天测试器接口
type ChatTester interface {
	TestChat(apiKey string, provider *config.ProviderInfo, model string) (*ChatTestResult, error)
}

// DefaultChatTester 默认聊天测试器实现
type DefaultChatTester struct {
	client *http.Client
}

// NewDefaultChatTester 创建默认聊天测试器
func NewDefaultChatTester() *DefaultChatTester {
	return &DefaultChatTester{
		client: &http.Client{
			Timeout: 30 * time.Second,
		},
	}
}

// TestChat 执行聊天测试
func (t *DefaultChatTester) TestChat(apiKey string, provider *config.ProviderInfo, model string) (*ChatTestResult, error) {
	startTime := time.Now()
	
	// 如果没有指定模型，使用默认模型
	if model == "" {
		model = provider.ChatModel
	}

	// 根据提供商类型构建请求
	var requestBody []byte
	var err error
	
	switch provider.Code {
	case "openai", "siliconflow", "openrouter", "together", "fireworks", "groq", "cerebras", "novita", "qwen", "moonshot", "bigmodel", "lingyiwanwu", "deepseek", "mistral":
		requestBody, err = t.buildOpenAIStyleRequest(model)
	case "claude":
		requestBody, err = t.buildClaudeRequest(model)
	case "gemini":
		requestBody, err = t.buildGeminiRequest(model)
	default:
		return &ChatTestResult{
			Success:      false,
			ResponseTime: int(time.Since(startTime).Milliseconds()),
			ErrorMessage: fmt.Sprintf("不支持的提供商: %s", provider.Code),
		}, nil
	}

	if err != nil {
		return &ChatTestResult{
			Success:      false,
			ResponseTime: int(time.Since(startTime).Milliseconds()),
			ErrorMessage: fmt.Sprintf("构建请求失败: %v", err),
		}, nil
	}

	// 构建完整的URL
	url := provider.APIEndpoint + provider.ChatEndpoint
	
	// 创建HTTP请求
	req, err := http.NewRequestWithContext(context.Background(), "POST", url, bytes.NewBuffer(requestBody))
	if err != nil {
		return &ChatTestResult{
			Success:      false,
			ResponseTime: int(time.Since(startTime).Milliseconds()),
			ErrorMessage: fmt.Sprintf("创建请求失败: %v", err),
		}, nil
	}

	// 设置请求头
	req.Header.Set("Content-Type", "application/json")
	
	// 设置认证（默认使用Bearer认证）
	req.Header.Set("Authorization", "Bearer "+apiKey)

	// 发送请求
	resp, err := t.client.Do(req)
	if err != nil {
		return &ChatTestResult{
			Success:      false,
			ResponseTime: int(time.Since(startTime).Milliseconds()),
			ErrorMessage: fmt.Sprintf("请求失败: %v", err),
		}, nil
	}
	defer resp.Body.Close()

	responseTime := int(time.Since(startTime).Milliseconds())

	// 读取响应
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return &ChatTestResult{
			Success:      false,
			ResponseTime: responseTime,
			ErrorMessage: fmt.Sprintf("读取响应失败: %v", err),
		}, nil
	}

	// 检查HTTP状态码
	if resp.StatusCode != http.StatusOK {
		return &ChatTestResult{
			Success:      false,
			ResponseTime: responseTime,
			ErrorMessage: fmt.Sprintf("API返回错误: %d, %s", resp.StatusCode, string(body)),
		}, nil
	}

	// 解析响应获取AI回复
	response, err := t.parseResponse(provider.Code, body)
	if err != nil {
		return &ChatTestResult{
			Success:      false,
			ResponseTime: responseTime,
			ErrorMessage: fmt.Sprintf("解析响应失败: %v", err),
		}, nil
	}

	return &ChatTestResult{
		Success:      true,
		ResponseTime: responseTime,
		Response:     response,
		Model:        model,
	}, nil
}

// buildOpenAIStyleRequest 构建OpenAI风格的请求
func (t *DefaultChatTester) buildOpenAIStyleRequest(model string) ([]byte, error) {
	request := map[string]interface{}{
		"model": model,
		"messages": []map[string]string{
			{
				"role":    "user",
				"content": "Hello! Please reply with a simple greeting.",
			},
		},
		"max_tokens":   50,
		"temperature": 0.7,
	}
	return json.Marshal(request)
}

// buildClaudeRequest 构建Claude风格的请求
func (t *DefaultChatTester) buildClaudeRequest(model string) ([]byte, error) {
	request := map[string]interface{}{
		"model": model,
		"messages": []map[string]string{
			{
				"role":    "user",
				"content": "Hello! Please reply with a simple greeting.",
			},
		},
		"max_tokens": 50,
	}
	return json.Marshal(request)
}

// buildGeminiRequest 构建Gemini风格的请求
func (t *DefaultChatTester) buildGeminiRequest(model string) ([]byte, error) {
	request := map[string]interface{}{
		"contents": []map[string]interface{}{
			{
				"parts": []map[string]string{
					{
						"text": "Hello! Please reply with a simple greeting.",
					},
				},
			},
		},
		"generationConfig": map[string]interface{}{
			"maxOutputTokens": 50,
			"temperature":     0.7,
		},
	}
	return json.Marshal(request)
}

// parseResponse 解析不同提供商的响应
func (t *DefaultChatTester) parseResponse(providerCode string, body []byte) (string, error) {
	switch providerCode {
	case "openai", "siliconflow", "openrouter", "together", "fireworks", "groq", "cerebras", "novita", "qwen", "moonshot", "bigmodel", "lingyiwanwu", "deepseek", "mistral":
		return t.parseOpenAIStyleResponse(body)
	case "claude":
		return t.parseClaudeResponse(body)
	case "gemini":
		return t.parseGeminiResponse(body)
	default:
		return "", fmt.Errorf("不支持的提供商: %s", providerCode)
	}
}

// parseOpenAIStyleResponse 解析OpenAI风格的响应
func (t *DefaultChatTester) parseOpenAIStyleResponse(body []byte) (string, error) {
	var response struct {
		Choices []struct {
			Message struct {
				Content string `json:"content"`
			} `json:"message"`
		} `json:"choices"`
	}
	
	if err := json.Unmarshal(body, &response); err != nil {
		return "", err
	}
	
	if len(response.Choices) == 0 {
		return "", fmt.Errorf("没有收到回复")
	}
	
	return response.Choices[0].Message.Content, nil
}

// parseClaudeResponse 解析Claude的响应
func (t *DefaultChatTester) parseClaudeResponse(body []byte) (string, error) {
	var response struct {
		Content []struct {
			Text string `json:"text"`
		} `json:"content"`
	}
	
	if err := json.Unmarshal(body, &response); err != nil {
		return "", err
	}
	
	if len(response.Content) == 0 {
		return "", fmt.Errorf("没有收到回复")
	}
	
	return response.Content[0].Text, nil
}

// parseGeminiResponse 解析Gemini的响应
func (t *DefaultChatTester) parseGeminiResponse(body []byte) (string, error) {
	var response struct {
		Candidates []struct {
			Content struct {
				Parts []struct {
					Text string `json:"text"`
				} `json:"parts"`
			} `json:"content"`
		} `json:"candidates"`
	}
	
	if err := json.Unmarshal(body, &response); err != nil {
		return "", err
	}
	
	if len(response.Candidates) == 0 || len(response.Candidates[0].Content.Parts) == 0 {
		return "", fmt.Errorf("没有收到回复")
	}
	
	return response.Candidates[0].Content.Parts[0].Text, nil
}
