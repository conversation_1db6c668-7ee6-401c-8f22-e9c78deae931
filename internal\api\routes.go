package api

import (
	"keyhub/internal/api/handlers"
	"keyhub/internal/services"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// SetupRoutes 设置API路由
func SetupRoutes(db *gorm.DB, queueService *services.QueueService) *gin.Engine {
	router := gin.Default()

	// 启用CORS
	router.Use(func(c *gin.Context) {
		c.<PERSON><PERSON>("Access-Control-Allow-Origin", "*")
		c.<PERSON><PERSON>("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		c.<PERSON>er("Access-Control-Allow-Headers", "Content-Type, Authorization")

		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(204)
			return
		}

		c.Next()
	})

	// 初始化处理器
	apiKeyHandler := handlers.NewAPIKeyHandler(db)
	testHandler := handlers.NewTestHandler(db, queueService)
	balanceHandler := handlers.NewBalanceHandler(db, queueService)
	queueHandler := handlers.NewQueueHandler(queueService)

	// API路由组
	api := router.Group("/api/v1")
	{
		// API Key管理
		apiKeys := api.Group("/apikeys")
		{
			apiKeys.GET("", apiKeyHandler.GetAll)
			apiKeys.POST("", apiKeyHandler.Create) // 支持单个和批量创建
			apiKeys.DELETE("", apiKeyHandler.Delete) // 支持单个和批量删除
			apiKeys.GET("/:id", apiKeyHandler.GetByID)
			apiKeys.PUT("/:id", apiKeyHandler.Update)
		}

		// 提供商信息
		api.GET("/providers", apiKeyHandler.GetProviders)

		// 测试相关
		tests := api.Group("/tests")
		{
			tests.POST("", testHandler.Test) // 统一测试接口，支持单个和批量
			tests.GET("/results", testHandler.GetResults)
		}

		// 余额查询
		balance := api.Group("/balance")
		{
			balance.POST("", balanceHandler.Check)                // 统一余额查询接口，支持单个和批量
			balance.GET("/history/:id", balanceHandler.GetHistory)
		}

		// 队列管理
		queue := api.Group("/queue")
		{
			queue.GET("/tasks", queueHandler.GetTasks)
			queue.GET("/tasks/:id", queueHandler.GetTaskStatus)
			queue.POST("/tasks/:id/cancel", queueHandler.CancelTask)
			queue.GET("/stats", queueHandler.GetQueueStats)
		}
	}

	// 健康检查
	router.GET("/health", func(c *gin.Context) {
		c.JSON(200, gin.H{"status": "ok"})
	})

	return router
}
