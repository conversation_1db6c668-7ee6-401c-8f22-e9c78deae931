package handlers

import (
	"net/http"
	"strconv"

	"keyhub/internal/models"
	"keyhub/internal/services"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type QueueHandler struct {
	queueService *services.QueueService
}

func NewQueueHandler(queueService *services.QueueService) *QueueHandler {
	return &QueueHandler{
		queueService: queueService,
	}
}

// GetTaskStatus 获取任务状态
func (h *QueueHandler) GetTaskStatus(c *gin.Context) {
	taskIDStr := c.Param("id")
	taskID, err := strconv.ParseUint(taskIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的任务ID"})
		return
	}

	task, err := h.queueService.GetTaskStatus(uint(taskID))
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			c.<PERSON>(http.StatusNotFound, gin.H{"error": "任务不存在"})
		} else {
			c.JSO<PERSON>(http.StatusInternalServerError, gin.H{"error": err.Error()})
		}
		return
	}

	c.JSON(http.StatusOK, task)
}

// GetTasks 获取任务列表
func (h *QueueHandler) GetTasks(c *gin.Context) {
	status := c.Query("status")
	limitStr := c.DefaultQuery("limit", "50")
	
	limit, err := strconv.Atoi(limitStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的limit参数"})
		return
	}

	var tasks []models.Task
	if status != "" {
		tasks, err = h.queueService.GetTasksByStatus(models.TaskStatus(status), limit)
	} else {
		// 获取所有任务
		tasks, err = h.queueService.GetTasksByStatus("", limit)
	}

	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"tasks": tasks,
		"count": len(tasks),
	})
}

// CancelTask 取消任务
func (h *QueueHandler) CancelTask(c *gin.Context) {
	taskIDStr := c.Param("id")
	taskID, err := strconv.ParseUint(taskIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的任务ID"})
		return
	}

	err = h.queueService.CancelTask(uint(taskID))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "任务已取消"})
}

// GetQueueStats 获取队列统计信息
func (h *QueueHandler) GetQueueStats(c *gin.Context) {
	stats := make(map[string]interface{})

	// 统计各状态的任务数量
	statuses := []models.TaskStatus{
		models.TaskStatusPending,
		models.TaskStatusRunning,
		models.TaskStatusCompleted,
		models.TaskStatusFailed,
		models.TaskStatusCancelled,
	}

	for _, status := range statuses {
		tasks, err := h.queueService.GetTasksByStatus(status, 0)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}
		stats[string(status)] = len(tasks)
	}

	c.JSON(http.StatusOK, stats)
}
