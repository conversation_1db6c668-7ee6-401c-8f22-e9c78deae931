import { Card, Statistic } from 'antd'
import type { StatisticProps } from 'antd'

interface StatisticCardProps extends StatisticProps {
  cardStyle?: React.CSSProperties
}

const StatisticCard: React.FC<StatisticCardProps> = ({
  cardStyle,
  ...statisticProps
}) => {
  return (
    <Card 
      styles={{ body: { padding: '16px' } }} 
      style={cardStyle}
    >
      <Statistic {...statisticProps} />
    </Card>
  )
}

export default StatisticCard
