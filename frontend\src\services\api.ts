import axios from 'axios'
import type { APIKey, Provider, Task, QueueStats } from '../types'

const API_BASE_URL = '/api/v1'

const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
})

// API请求和响应类型
interface APIResponse<T = any> {
  data: T
  message?: string
}

interface CreateAPIKeyData {
  name: string
  provider: string
  api_key: string
  description?: string
  is_batch?: boolean
}

interface UpdateAPIKeyData {
  name?: string
  provider?: string
  api_key?: string
  description?: string
}

interface TestData {
  api_key_id?: number
  api_key_ids?: number[]
  test_type: 'chat'
}

interface BalanceData {
  api_key_id?: number
  api_key_ids?: number[]
}

// API Key相关接口
export const apiKeyService = {
  getAll: (): Promise<APIResponse<APIKey[]>> => api.get('/apikeys'),
  create: (data: CreateAPIKeyData): Promise<APIResponse> => api.post('/apikeys', data),
  delete: (ids: number | number[]): Promise<APIResponse> => {
    const idsArray = Array.isArray(ids) ? ids : [ids]
    return api.delete('/apikeys', { data: { ids: idsArray } })
  },
  getById: (id: number): Promise<APIResponse<APIKey>> => api.get(`/apikeys/${id}`),
  update: (id: number, data: UpdateAPIKeyData): Promise<APIResponse> => api.put(`/apikeys/${id}`, data),
}

// 测试相关接口
export const testService = {
  test: (data: TestData): Promise<APIResponse> => api.post('/tests', data),
}

// 余额相关接口
export const balanceService = {
  check: (data: BalanceData): Promise<APIResponse> => api.post('/balance', data),
  getHistory: (id: number): Promise<APIResponse> => api.get(`/balance/history/${id}`),
}

// 队列相关接口
export const queueService = {
  getTasks: (status?: string, limit?: number): Promise<APIResponse<Task[]>> => {
    const params = new URLSearchParams()
    if (status) params.append('status', status)
    if (limit) params.append('limit', limit.toString())
    return api.get(`/queue/tasks?${params.toString()}`)
  },
  getTaskStatus: (taskId: number): Promise<APIResponse<Task>> => api.get(`/queue/tasks/${taskId}`),
  cancelTask: (taskId: number): Promise<APIResponse> => api.post(`/queue/tasks/${taskId}/cancel`),
  getStats: (): Promise<APIResponse<QueueStats>> => api.get('/queue/stats'),
}

// 提供商相关接口
export const providerService = {
  getAll: (): Promise<APIResponse<{ providers: Provider[] }>> => api.get('/providers'),
}

export default api
