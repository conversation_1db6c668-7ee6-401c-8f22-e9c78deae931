package services

// BalanceParser 余额解析器接口
type BalanceParser interface {
	ParseBalance(responseBody []byte) (*BalanceData, error)
}

// GetBalanceParser 根据提供商代码获取对应的余额解析器
func GetBalanceParser(providerCode string) BalanceParser {
	switch providerCode {
	case "siliconflow":
		return &SiliconFlowBalanceParser{}
	default:
		return &DefaultBalanceParser{}
	}
}

// DefaultBalanceParser 默认余额解析器（用于不支持余额查询的提供商）
type DefaultBalanceParser struct{}

// ParseBalance 默认解析方法，返回模拟数据
func (p *DefaultBalanceParser) ParseBalance(responseBody []byte) (*BalanceData, error) {
	return &BalanceData{
		TotalBalance: 100.0,
		GrantBalance: 10.0,
		PaidBalance:  90.0,
	}, nil
}
