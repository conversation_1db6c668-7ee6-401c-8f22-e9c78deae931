// 格式化余额显示
export const formatBalance = (balance: number, currency: string): string => {
  switch (currency) {
    case 'USD':
      return `$${balance.toFixed(2)}`
    case 'CNY':
      return `￥${balance.toFixed(2)}`
    case 'TOKEN':
      return `T ${balance.toFixed(0)}`
    default:
      return `$${balance.toFixed(2)}`
  }
}

// 搜索过滤函数
export const filterApiKeys = <T extends { name: string; api_key: string }>(
  items: T[],
  searchText: string
): T[] => {
  if (!searchText) return items
  const searchLower = searchText.toLowerCase()
  return items.filter(item =>
    item.name.toLowerCase().includes(searchLower) ||
    item.api_key.toLowerCase().includes(searchLower)
  )
}
