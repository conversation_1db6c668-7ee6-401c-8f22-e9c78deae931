package services

import (
	"fmt"
	"io/ioutil"
	"log"
	"net/http"
	"time"

	"keyhub/internal/config"
	"keyhub/internal/models"

	"gorm.io/gorm"
)

type BalanceService struct {
	db *gorm.DB
}

// BalanceData 余额查询结果
type BalanceData struct {
	TotalBalance float64 `json:"total_balance"` // API返回的总余额
	GrantBalance float64 `json:"grant_balance"` // 赠送余额
	PaidBalance  float64 `json:"paid_balance"`  // 充值余额
}

func NewBalanceService(db *gorm.DB) *BalanceService {
	return &BalanceService{db: db}
}

// CheckBalance 检查API Key余额
func (s *BalanceService) CheckBalance(apiKeyID uint) (*models.BalanceRecord, error) {
	// 获取API Key信息
	var apiKey models.APIKey
	if err := s.db.First(&apiKey, apiKeyID).Error; err != nil {
		return nil, err
	}

	// 获取原始API Key用于实际查询
	apiKeyService := NewAPIKeyService(s.db)
	rawAPIKey, err := apiKeyService.GetRawAPIKey(apiKeyID)
	if err != nil {
		return nil, err
	}



	// 获取提供商信息
	providerInfo := config.GetProviderByCode(apiKey.Provider)
	if providerInfo == nil {
		return nil, fmt.Errorf("不支持的提供商: %s", apiKey.Provider)
	}

	// 检查是否支持余额查询
	if !providerInfo.SupportBalance {
		return nil, fmt.Errorf("提供商 %s 不支持余额查询", providerInfo.Name)
	}

	// 根据提供商查询余额
	var totalBalance, grantBalance, paidBalance float64

	// 调用实际的余额查询API
	balanceData, err := s.queryProviderBalance(providerInfo, rawAPIKey)
	if err != nil {
		// 如果查询失败，记录错误但仍创建记录
		log.Printf("查询 %s 余额失败: %v", providerInfo.Name, err)
		totalBalance = 0.0
		grantBalance = 0.0
		paidBalance = 0.0
	} else {
		// 使用API返回的实际值
		totalBalance = balanceData.TotalBalance
		grantBalance = balanceData.GrantBalance
		paidBalance = balanceData.PaidBalance
	}

	now := time.Now()
	record := &models.BalanceRecord{
		APIKeyID:     apiKeyID,
		TotalBalance: totalBalance,
		GrantBalance: grantBalance,
		PaidBalance:  paidBalance,
		Currency:     providerInfo.Currency, // 使用提供商的币种配置
		CheckedAt:    now,
	}

	// 保存余额记录到BalanceRecord表
	err = s.db.Create(record).Error
	if err != nil {
		return record, err
	}

	// 更新APIKey表中的最新余额信息
	updateData := map[string]interface{}{
		"total_balance":       totalBalance,
		"grant_balance":       grantBalance,
		"paid_balance":        paidBalance,
		"last_balance_check":  &now,
		"updated_at":          now,
	}

	err = s.db.Model(&models.APIKey{}).Where("id = ?", apiKeyID).Updates(updateData).Error
	return record, err
}

// GetHistory 获取余额历史记录
func (s *BalanceService) GetHistory(apiKeyID uint) ([]models.BalanceRecord, error) {
	var records []models.BalanceRecord
	err := s.db.Where("api_key_id = ?", apiKeyID).
		Order("created_at desc").
		Find(&records).Error
	return records, err
}

// queryProviderBalance 查询提供商余额
func (s *BalanceService) queryProviderBalance(provider *config.ProviderInfo, apiKey string) (*BalanceData, error) {
	// 构建请求URL
	url := provider.APIEndpoint + provider.BalanceEndpoint

	// 创建HTTP请求（默认使用GET方法）
	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return nil, fmt.Errorf("创建请求失败: %v", err)
	}

	// 设置认证头（默认使用Bearer认证）
	req.Header.Set("Authorization", "Bearer "+apiKey)
	req.Header.Set("Content-Type", "application/json")

	// 发送请求
	client := &http.Client{Timeout: 10 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("请求失败: %v", err)
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %v", err)
	}

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("API返回错误: %d, %s", resp.StatusCode, string(body))
	}

	// 解析响应 - 使用对应的余额解析器
	parser := GetBalanceParser(provider.Code)
	return parser.ParseBalance(body)
}
