package main

import (
	"log"
	"os"
	"os/signal"
	"syscall"

	"keyhub/internal/api"
	"keyhub/internal/config"
	"keyhub/internal/database"
	"keyhub/internal/services"

	"github.com/gin-gonic/gin"
	"github.com/joho/godotenv"
)

func main() {
	// 加载环境变量
	if err := godotenv.Load(); err != nil {
		log.Println("No .env file found")
	}

	// 初始化配置
	cfg := config.Load()

	// 初始化数据库
	db, err := database.Initialize(cfg.DatabaseURL)
	if err != nil {
		log.Fatal("Failed to initialize database:", err)
	}

	// 获取底层的sql.DB实例用于关闭连接
	sqlDB, err := db.DB()
	if err != nil {
		log.Fatal("Failed to get database instance:", err)
	}
	defer sqlDB.Close()

	// 设置Gin模式
	if cfg.Environment == "production" {
		gin.SetMode(gin.ReleaseMode)
	}

	// 初始化队列服务
	queueService := services.NewQueueService(db)

	// 启动队列服务（3个工作器）
	queueService.Start(3)
	log.Println("队列服务已启动，工作器数量: 3")

	// 初始化路由
	router := api.SetupRoutes(db, queueService)

	// 设置优雅关闭
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)

	// 启动服务器
	port := os.Getenv("PORT")
	if port == "" {
		port = "8888"  // 更换为8888端口
	}

	log.Printf("Server starting on port %s", port)

	// 在goroutine中启动服务器
	go func() {
		// 明确监听IPv4地址
		if err := router.Run("0.0.0.0:" + port); err != nil {
			log.Fatal("Failed to start server:", err)
		}
	}()

	// 等待中断信号
	<-quit
	log.Println("正在关闭服务器...")

	// 停止队列服务
	queueService.Stop()
	log.Println("服务器已关闭")
}
