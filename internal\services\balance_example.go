package services

import (
	"encoding/json"
	"fmt"
)

// 示例：如何为新的提供商添加余额解析器
// 复制此文件并重命名为 balance_[provider].go，然后修改相应的逻辑

// ExampleBalanceParser 示例余额解析器
type ExampleBalanceParser struct{}

// ExampleResponse 示例API响应结构（只定义需要的字段）
type ExampleResponse struct {
	Success bool `json:"success"`
	Data    struct {
		Balance     float64 `json:"balance"`      // 总余额
		FreeBalance float64 `json:"free_balance"` // 赠送余额
		PaidBalance float64 `json:"paid_balance"` // 充值余额
	} `json:"data"`
	Message string `json:"message"`
}

// ParseBalance 解析示例提供商的余额响应
func (p *ExampleBalanceParser) ParseBalance(responseBody []byte) (*BalanceData, error) {
	var response ExampleResponse

	if err := json.Unmarshal(responseBody, &response); err != nil {
		return nil, fmt.Errorf("解析示例提供商响应失败: %v", err)
	}

	// 检查API响应状态
	if !response.Success {
		return nil, fmt.Errorf("示例提供商API返回错误: %s", response.Message)
	}

	return &BalanceData{
		TotalBalance: response.Data.Balance,     // 总余额
		GrantBalance: response.Data.FreeBalance, // 赠送余额
		PaidBalance:  response.Data.PaidBalance, // 充值余额
	}, nil
}

// 添加新提供商的步骤：
// 1. 复制此文件并重命名为 balance_[provider].go
// 2. 修改结构体名称和响应结构
// 3. 实现 ParseBalance 方法
// 4. 在 balance_parser.go 的 GetBalanceParser 函数中添加新的 case
// 5. 在 providers.go 中配置提供商的余额查询信息
