package handlers

import (
	"net/http"

	"keyhub/internal/services"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type TestHandler struct {
	service      *services.TestService
	queueService *services.QueueService
}

func NewTestHandler(db *gorm.DB, queueService *services.QueueService) *TestHandler {
	return &TestHandler{
		service:      services.NewTestService(db),
		queueService: queueService,
	}
}

// Test 统一测试接口（支持单个和批量）
func (h *TestHandler) Test(c *gin.Context) {
	var request struct {
		APIKeyID  *uint   `json:"api_key_id,omitempty"`  // 单个测试时使用
		APIKeyIDs []uint  `json:"api_key_ids,omitempty"` // 批量测试时使用
		TestType  string  `json:"test_type"`             // chat
		Model     string  `json:"model"`                 // 可选，指定测试模型
		Priority  int     `json:"priority"`              // 可选，任务优先级
	}

	if err := c.ShouldBindJ<PERSON>(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// 设置默认优先级
	if request.Priority == 0 {
		request.Priority = 5 // 中等优先级
	}

	// 判断是单个测试还是批量测试
	if request.APIKeyID != nil {
		// 单个测试
		task, err := h.queueService.EnqueueTest(*request.APIKeyID, request.TestType, request.Model, request.Priority)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}

		c.JSON(http.StatusAccepted, gin.H{
			"message": "测试任务已添加到队列",
			"task_id": task.ID,
			"status":  task.Status,
			"count":   1,
		})
	} else if len(request.APIKeyIDs) > 0 {
		// 批量测试
		tasks, err := h.queueService.EnqueueBatchTest(request.APIKeyIDs, request.TestType, request.Model, request.Priority)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}

		// 提取任务ID
		taskIDs := make([]uint, len(tasks))
		for i, task := range tasks {
			taskIDs[i] = task.ID
		}

		c.JSON(http.StatusAccepted, gin.H{
			"message":  "批量测试任务已添加到队列",
			"task_ids": taskIDs,
			"count":    len(tasks),
		})
	} else {
		c.JSON(http.StatusBadRequest, gin.H{"error": "必须提供 api_key_id 或 api_key_ids"})
	}
}

// GetResults 获取测试结果
func (h *TestHandler) GetResults(c *gin.Context) {
	results, err := h.service.GetResults()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, results)
}
