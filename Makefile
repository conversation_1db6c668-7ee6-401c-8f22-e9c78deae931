# KeyHub项目Makefile (Windows适配)

.PHONY: help dev build clean install-deps

# 默认目标
help:
	@echo "KeyHub项目命令:"
	@echo "  install-deps - 安装依赖"
	@echo "  dev-backend  - 启动后端服务"
	@echo "  dev-frontend - 启动前端服务"
	@echo "  build        - 构建项目"
	@echo "  clean        - 清理构建文件"

# 安装依赖
install-deps:
	@echo "安装Go依赖..."
	@go mod tidy
	@echo "安装前端依赖..."
	@cd frontend && npm install

# 启动后端开发服务
dev-backend:
	@echo "启动后端服务..."
	@go run cmd/server/main.go

# 启动前端开发服务
dev-frontend:
	@echo "启动前端服务..."
	@cd frontend && npm run dev

# 构建项目
build:
	@echo "构建后端..."
	@go build -o bin/keyhub.exe cmd/server/main.go
	@echo "构建前端..."
	@cd frontend && npm run build

# 清理
clean:
	@echo "清理构建文件..."
	@if exist bin rmdir /s /q bin
	@if exist frontend\dist rmdir /s /q frontend\dist
	@if exist *.db del *.db
