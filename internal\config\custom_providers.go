package config

// GetCustomProviders 获取自定义提供商列表
// 用户可以在这里添加新的AI服务提供商
func GetCustomProviders() []ProviderInfo {
	return []ProviderInfo{
		// 在这里添加自定义提供商配置
	}
}

// GetAllProviders 获取所有提供商（内置+自定义）
func GetAllProviders() []ProviderInfo {
	providers := GetSupportedProviders()
	customProviders := GetCustomProviders()
	
	// 合并内置和自定义提供商
	allProviders := make([]ProviderInfo, 0, len(providers)+len(customProviders))
	allProviders = append(allProviders, providers...)
	allProviders = append(allProviders, customProviders...)
	
	return allProviders
}
