import React, { useState, useEffect } from 'react'
import { Table, Button, Space, Tag, Modal, Form, Input, Select, Popconfirm, Card, Tooltip, Badge, Checkbox, App, Dropdown } from 'antd'
import type { ColumnsType } from 'antd/es/table'
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  Play<PERSON>ircleOutlined,
  DollarOutlined,
  ReloadOutlined,
  LoadingOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  MinusCircleOutlined,
  SettingOutlined,
  SearchOutlined
} from '@ant-design/icons'
import { apiKeyService, providerService, testService, balanceService } from '../services/api'
import { APIKey, Provider } from '../types'
import { useResponsive } from '../hooks/useResponsive'
import { useSetState } from '../hooks/useSetState'
import { DEFAULT_HIDDEN_COLUMNS } from '../constants'
import { formatBalance, filterApiKeys } from '../utils'

const { Option } = Select

const APIKeys: React.FC = () => {
  const { message } = App.useApp()
  const [apiKeys, setApiKeys] = useState<APIKey[]>([])
  const [providers, setProviders] = useState<Provider[]>([])
  const [loading, setLoading] = useState(false)
  const [modalVisible, setModalVisible] = useState(false)
  const [editingKey, setEditingKey] = useState<APIKey | null>(null)
  const [form] = Form.useForm()
  const testingKeys = useSetState<number>()
  const balanceLoadingKeys = useSetState<number>()
  const [selectedRowKeys, setSelectedRowKeys] = useState<number[]>([])
  const [batchOperating, setBatchOperating] = useState(false)
  // 添加防重复点击的状态
  const [batchTestRequesting, setBatchTestRequesting] = useState(false)
  const [batchBalanceRequesting, setBatchBalanceRequesting] = useState(false)
  // 控制列的显示/隐藏
  const hiddenColumns = useSetState<string>(new Set(DEFAULT_HIDDEN_COLUMNS))
  // 搜索筛选
  const [searchText, setSearchText] = useState('')
  const isMobile = useResponsive(768)

  // 分页配置
  const paginationConfig = {
    showSizeChanger: !isMobile,
    showQuickJumper: !isMobile,
    showTotal: (total: number, range?: [number, number]) => {
      return isMobile ? `${total} 条` : `第 ${range?.[0]}-${range?.[1]} 条，共 ${total} 条记录`
    },
    simple: isMobile,
  }



  // 获取API Key列表
  const fetchAPIKeys = async () => {
    setLoading(true)
    try {
      const response = await apiKeyService.getAll()
      setApiKeys(response.data)
    } catch (error) {
      message.error('获取API Key列表失败')
    } finally {
      setLoading(false)
    }
  }

  // 获取提供商列表
  const fetchProviders = async () => {
    try {
      const response = await providerService.getAll()
      setProviders(response.data.providers || response.data)
    } catch (error) {
      message.error('获取提供商列表失败')
    }
  }

  useEffect(() => {
    fetchAPIKeys()
    fetchProviders()
  }, [])





  // 打开添加/编辑模态框
  const openModal = (key?: APIKey) => {
    setEditingKey(key || null)
    setModalVisible(true)
    if (key) {
      form.setFieldsValue({
        name: key.name,
        provider: key.provider,
        description: key.description,
        is_batch: false, // 编辑模式不支持批量
      })
    } else {
      form.resetFields()
      form.setFieldsValue({
        is_batch: false, // 默认单个模式
      })
    }
  }

  // 关闭模态框
  const closeModal = () => {
    setModalVisible(false)
    setEditingKey(null)
    form.resetFields()
  }

  // 提交表单
  const handleSubmit = async (values: any) => {
    try {
      if (editingKey) {
        // 更新
        await apiKeyService.update(editingKey.id, values)
        message.success('API Key更新成功')
      } else {
        // 创建（单个或批量）
        const response = await apiKeyService.create({
          name: values.name,
          provider: values.provider,
          api_key: values.api_key,
          description: values.description,
          is_batch: values.is_batch || false // 从表单获取批量标识
        })

        if (response.data.is_batch) {
          // 批量创建结果处理
          const result = response.data.result
          let messageContent = response.data.message

          // 如果有跳过或无效的Key，显示详细信息
          if (result.skipped_count > 0 || result.invalid_count > 0) {
            const details = []
            if (result.skipped_keys && result.skipped_keys.length > 0) {
              details.push(`重复的Key: ${result.skipped_keys.join(', ')}`)
            }
            if (result.invalid_keys && result.invalid_keys.length > 0) {
              details.push(`无效的Key: ${result.invalid_keys.join(', ')}`)
            }

            if (details.length > 0) {
              messageContent += `\n详细信息:\n${details.join('\n')}`
            }
          }

          if (result.created_count > 0) {
            message.success(messageContent)
          } else {
            message.warning(messageContent)
          }
        } else {
          // 单个创建结果处理
          message.success(response.data.message)
        }
      }
      closeModal()
      fetchAPIKeys()
    } catch (error: any) {
      message.error(error.response?.data?.error || '操作失败')
    }
  }

  // 删除API Key
  const handleDelete = async (id: number) => {
    try {
      await apiKeyService.delete(id)
      message.success('API Key删除成功')
      fetchAPIKeys()
    } catch (error: any) {
      message.error(error.response?.data?.error || '删除失败')
    }
  }

  // 测试API Key
  const handleTest = async (apiKey: APIKey) => {
    testingKeys.add(apiKey.id)
    try {
      const response = await testService.test({
        api_key_id: apiKey.id,
        test_type: 'chat'
      })

      // 现在接口返回的是任务状态，不是测试结果
      if (response.data.task_id) {
        message.success(`${apiKey.name} 测试任务已提交，正在后台执行...`)

        setTimeout(() => {
          fetchAPIKeys()
          testingKeys.remove(apiKey.id)
        }, 3000)
      } else {
        message.error(`${apiKey.name} 测试任务提交失败`)
        testingKeys.remove(apiKey.id)
      }
    } catch (error: any) {
      message.error(`测试失败: ${error.response?.data?.error || error.message || '操作失败'}`)
      testingKeys.remove(apiKey.id)
    }
  }

  // 查询余额
  const handleCheckBalance = async (apiKey: APIKey) => {
    balanceLoadingKeys.add(apiKey.id)
    try {
      const response = await balanceService.check({
        api_key_id: apiKey.id
      })

      // 现在接口返回的是任务状态，不是查询结果
      if (response.data.task_id) {
        message.success(`${apiKey.name} 余额查询任务已提交，正在后台执行...`)

        setTimeout(() => {
          fetchAPIKeys()
          balanceLoadingKeys.remove(apiKey.id)
        }, 3000)
      } else {
        message.error(`${apiKey.name} 余额查询任务提交失败`)
        balanceLoadingKeys.remove(apiKey.id)
      }
    } catch (error: any) {
      message.error(`余额查询失败: ${error.response?.data?.error || error.message || '操作失败'}`)
      balanceLoadingKeys.remove(apiKey.id)
    }
  }



  // 通用的余额渲染函数
  const renderBalance = (balance: number, record: APIKey, showTooltip = false) => {
    if (balanceLoadingKeys.has(record.id)) {
      return <Badge status="processing" text="查询中..." />
    }
    if (balance !== undefined && balance !== null) {
      const providerInfo = providers.find(p => p.code === record.provider)
      const currency = providerInfo?.currency || 'USD'
      const formattedBalance = formatBalance(balance, currency)

      if (showTooltip && record.last_balance_check) {
        const checkTime = new Date(record.last_balance_check).toLocaleString()
        return (
          <Tooltip title={`币种: ${currency}\n查询时间: ${checkTime}`}>
            <span>{formattedBalance}</span>
          </Tooltip>
        )
      }
      return <span>{formattedBalance}</span>
    }
    return <Badge status="default" text="未查询" />
  }

  const columns: ColumnsType<APIKey> = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
      sorter: (a: APIKey, b: APIKey) => a.id - b.id,
      sortDirections: ['ascend', 'descend'] as const,
    },
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name',
      width: 120,
      sorter: (a: APIKey, b: APIKey) => a.name.localeCompare(b.name),
      sortDirections: ['ascend', 'descend'] as const,
    },
    {
      title: '提供商',
      dataIndex: 'provider',
      key: 'provider',
      width: 120,
      render: (provider: string) => {
        const providerInfo = providers.find(p => p.code === provider)
        return (
          <Tag color={providerInfo?.color || 'default'}>
            {provider.toUpperCase()}
          </Tag>
        )
      },
      sorter: (a: APIKey, b: APIKey) => a.provider.localeCompare(b.provider),
      filters: providers.map(provider => ({
        text: provider.name,
        value: provider.code,
      })),
      onFilter: (value, record) => record.provider === value,
    },
    {
      title: 'API Key',
      dataIndex: 'api_key',
      key: 'api_key',
      width: 120,
      render: (key: string) => (
        <span style={{ fontFamily: 'monospace' }}>{key}</span>
      ),
      ellipsis: true,
    },
    {
      title: '状态',
      key: 'test_status',
      width: 60,
      render: (_: any, record: APIKey) => {
        if (testingKeys.has(record.id)) {
          return (
            <Tooltip title="测试中...">
              <LoadingOutlined style={{ color: '#1890ff' }} />
            </Tooltip>
          )
        }
        if (record.last_test_status !== undefined && record.last_test_status !== null) {
          const isSuccess = record.last_test_status === true
          const testTime = record.last_test_time ? new Date(record.last_test_time).toLocaleString() : ''
          const tooltipTitle = isSuccess
            ? `测试通过\n响应时间: ${record.last_response_time}ms\n测试时间: ${testTime}`
            : `测试失败\n错误: ${record.last_test_error}\n测试时间: ${testTime}`
          return (
            <Tooltip title={tooltipTitle}>
              {isSuccess ? (
                <CheckCircleOutlined style={{ color: '#52c41a' }} />
              ) : (
                <CloseCircleOutlined style={{ color: '#ff4d4f' }} />
              )}
            </Tooltip>
          )
        }
        return (
          <Tooltip title="未测试">
            <MinusCircleOutlined style={{ color: '#d9d9d9' }} />
          </Tooltip>
        )
      },
    },
    {
      title: '总余额',
      dataIndex: 'total_balance',
      key: 'total_balance',
      width: 80,
      render: (balance: number, record: APIKey) => renderBalance(balance, record, true),
      sorter: (a: APIKey, b: APIKey) => (a.total_balance || 0) - (b.total_balance || 0),
      sortDirections: ['ascend', 'descend'] as const,
      ellipsis: true,
    },
    {
      title: '充值余额',
      dataIndex: 'paid_balance',
      key: 'paid_balance',
      width: 80,
      render: (balance: number, record: APIKey) => renderBalance(balance, record),
      sorter: (a: APIKey, b: APIKey) => (a.paid_balance || 0) - (b.paid_balance || 0),
      sortDirections: ['ascend', 'descend'] as const,
      ellipsis: true,
    },
    {
      title: '赠送余额',
      dataIndex: 'grant_balance',
      key: 'grant_balance',
      width: 80,
      render: (balance: number, record: APIKey) => renderBalance(balance, record),
      sorter: (a: APIKey, b: APIKey) => (a.grant_balance || 0) - (b.grant_balance || 0),
      sortDirections: ['ascend', 'descend'] as const,
      ellipsis: true,
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      width: 150,
      ellipsis: true,
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 130,
      render: (timestamp: number) => new Date(timestamp).toLocaleString(),
      sorter: (a: APIKey, b: APIKey) => a.created_at - b.created_at,
      defaultSortOrder: 'descend' as const,
      ellipsis: true,
    },
    {
      title: '操作',
      key: 'action',
      width: 200,
      render: (_: any, record: APIKey) => (
        <Space size="small" wrap>
          <Tooltip title="测试API Key连通性">
            <Button
              type="primary"
              size="small"
              icon={testingKeys.has(record.id) ? <LoadingOutlined /> : <PlayCircleOutlined />}
              onClick={() => handleTest(record)}
              loading={testingKeys.has(record.id)}
            />
          </Tooltip>

          <Tooltip title="查询账户余额">
            <Button
              type="default"
              size="small"
              icon={balanceLoadingKeys.has(record.id) ? <LoadingOutlined /> : <DollarOutlined />}
              onClick={() => handleCheckBalance(record)}
              loading={balanceLoadingKeys.has(record.id)}
            />
          </Tooltip>

          <Tooltip title="编辑API Key">
            <Button
              type="link"
              size="small"
              icon={<EditOutlined />}
              onClick={() => openModal(record)}
            />
          </Tooltip>

          <Popconfirm
            title="确定要删除这个API Key吗？"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Tooltip title="删除API Key">
              <Button type="link" size="small" danger icon={<DeleteOutlined />} />
            </Tooltip>
          </Popconfirm>
        </Space>
      ),
    },
  ]

  // 过滤掉隐藏的列
  const visibleColumns = columns.filter(column => !hiddenColumns.has(column.key as string))

  // 搜索过滤数据
  const filteredApiKeys = filterApiKeys(apiKeys, searchText)

  // 切换列的显示/隐藏
  const toggleColumn = (columnKey: string) => {
    hiddenColumns.toggle(columnKey)
  }

  // 全选/全不选
  const toggleAllColumns = () => {
    const allColumnKeys = adjustableColumns.map(col => col.key)
    const allHidden = allColumnKeys.every(key => hiddenColumns.has(key))

    if (allHidden) {
      // 如果全部隐藏，则显示所有列
      hiddenColumns.clear()
    } else {
      // 否则隐藏所有列
      hiddenColumns.setState(new Set(allColumnKeys))
    }
  }

  // 可调整的列配置（除了ID，其他都可调整）
  const adjustableColumns = [
    { key: 'name', title: '名称' },
    { key: 'provider', title: '提供商' },
    { key: 'api_key', title: 'API Key' },
    { key: 'test_status', title: '状态' },
    { key: 'total_balance', title: '总余额' },
    { key: 'paid_balance', title: '充值余额' },
    { key: 'grant_balance', title: '赠送余额' },
    { key: 'description', title: '描述' },
    { key: 'created_at', title: '创建时间' },
    { key: 'action', title: '操作' },
  ]

  // 列设置菜单项
  const allColumnKeys = adjustableColumns.map(col => col.key)
  const allVisible = allColumnKeys.every(key => !hiddenColumns.has(key))
  const allHidden = allColumnKeys.every(key => hiddenColumns.has(key))
  const indeterminate = !allVisible && !allHidden

  const columnMenuItems = [
    // 全选选项
    {
      key: 'select-all',
      label: (
        <Checkbox
          checked={allVisible}
          indeterminate={indeterminate}
          onChange={toggleAllColumns}
        >
          <strong>全选</strong>
        </Checkbox>
      ),
    },
    {
      type: 'divider' as const,
    },
    // 各个列选项
    ...adjustableColumns.map(col => ({
      key: col.key,
      label: (
        <Checkbox
          checked={!hiddenColumns.has(col.key)}
          onChange={() => toggleColumn(col.key)}
        >
          {col.title}
        </Checkbox>
      ),
    }))
  ]

  // 批量测试选中的API Key
  const handleBatchTest = async () => {
    // 防止重复点击
    if (batchTestRequesting || batchOperating) {
      return
    }

    const targetKeys = selectedRowKeys.length > 0 ? selectedRowKeys : apiKeys.map(key => key.id)

    if (targetKeys.length === 0) {
      message.warning('没有可测试的API Key')
      return
    }

    // 如果是批量测试所有API Key（没有选中任何项），需要确认
    if (selectedRowKeys.length === 0) {
      const confirmed = window.confirm(`批量测试确认\n\n确定要测试所有 ${targetKeys.length} 个API Key吗？\n\n这将对所有API Key进行连通性测试。`)
      if (!confirmed) {
        return
      }
    }

    // 立即设置请求状态，防止重复点击
    setBatchTestRequesting(true)
    setBatchOperating(true)

    try {
      const response = await testService.test({
        api_key_ids: targetKeys,
        test_type: 'chat'
      })

      // 检查响应中是否有重复任务的信息
      if (response.data.message) {
        message.success(response.data.message)
      } else {
        message.success(`批量测试任务已提交，测试了 ${targetKeys.length} 个API Key`)
      }

      // 重新加载数据以获取最新的测试结果
      fetchAPIKeys()
      setSelectedRowKeys([]) // 清空选择
    } catch (error: any) {
      message.error(`批量测试失败: ${error.response?.data?.error || error.message || '操作失败'}`)
    } finally {
      setBatchTestRequesting(false)
      setBatchOperating(false)
    }
  }

  // 批量查询余额
  const handleBatchBalance = async () => {
    // 防止重复点击
    if (batchBalanceRequesting || batchOperating) {
      return
    }

    if (selectedRowKeys.length === 0) {
      message.warning('请先选择要查询余额的API Key')
      return
    }

    // 立即设置请求状态，防止重复点击
    setBatchBalanceRequesting(true)
    setBatchOperating(true)

    try {
      const response = await balanceService.check({
        api_key_ids: selectedRowKeys
      })

      // 检查响应中是否有任务信息
      if (response.data.message) {
        message.success(response.data.message)
      } else {
        message.success(`批量余额查询任务已提交，共 ${selectedRowKeys.length} 个任务正在后台执行...`)
      }

      // 延迟后刷新数据
      setTimeout(() => {
        fetchAPIKeys()
      }, 4000)

      setSelectedRowKeys([]) // 清空选择
    } catch (error: any) {
      message.error(`批量余额查询失败: ${error.response?.data?.error || error.message || '操作失败'}`)
    } finally {
      setBatchBalanceRequesting(false)
      setBatchOperating(false)
    }
  }

  // 批量删除
  const handleBatchDelete = async () => {
    if (selectedRowKeys.length === 0) {
      message.warning('请先选择要删除的API Key')
      return
    }

    // 使用原生confirm对话框
    const confirmed = window.confirm(`删除确认\n\n确定要删除选中的 ${selectedRowKeys.length} 个API Key吗？\n\n此操作不可恢复，请谨慎操作。`)

    if (confirmed) {
      setBatchOperating(true)
      try {
        const response = await apiKeyService.delete(selectedRowKeys)
        message.success(response.data.message)
        fetchAPIKeys()
        setSelectedRowKeys([]) // 清空选择
      } catch (error: any) {
        message.error(`批量删除失败: ${error.response?.data?.error || error.message || '操作失败'}`)
      } finally {
        setBatchOperating(false)
      }
    }
  }

  return (
    <div>
      <Card
        style={{ marginBottom: 16 }}
        styles={{
          body: { padding: '12px 16px' }
        }}
      >
        <div style={{
          display: 'flex',
          flexDirection: isMobile ? 'column' : 'row',
          gap: '12px',
          alignItems: 'flex-start',
          justifyContent: 'space-between'
        }}>
          <div>
            <h1 style={{
              margin: 0,
              fontSize: '22px',
              fontWeight: 600
            }}>
              API Key 综合管理
            </h1>
            {!isMobile && (
              <p style={{
                margin: '8px 0 0 0',
                color: '#666',
                fontSize: '14px'
              }}>
                管理您的API密钥，支持连通性测试和余额查询
              </p>
            )}
          </div>
          <div style={{
            display: 'flex',
            width: isMobile ? '100%' : 'auto'
          }}>
            <Space wrap size="small" style={{ width: '100%' }}>
            <Button
              size="small"
              icon={<PlayCircleOutlined />}
              onClick={handleBatchTest}
              loading={batchTestRequesting || batchOperating}
              disabled={batchTestRequesting || batchBalanceRequesting}
              title={selectedRowKeys.length > 0 ? `测试选中(${selectedRowKeys.length})` : '批量测试'}
            >
              {isMobile ? (selectedRowKeys.length > 0 ? selectedRowKeys.length.toString() : '') : (selectedRowKeys.length > 0 ? `测试选中(${selectedRowKeys.length})` : '批量测试')}
            </Button>
            <Button
              size="small"
              icon={<DollarOutlined />}
              onClick={handleBatchBalance}
              disabled={selectedRowKeys.length === 0 || batchTestRequesting || batchBalanceRequesting}
              loading={batchBalanceRequesting || batchOperating}
              title={`批量查余额(${selectedRowKeys.length})`}
            >
              {isMobile ? selectedRowKeys.length.toString() : `批量查余额(${selectedRowKeys.length})`}
            </Button>
            <Button
              size="small"
              danger
              icon={<DeleteOutlined />}
              onClick={handleBatchDelete}
              disabled={selectedRowKeys.length === 0}
              loading={batchOperating}
              title={`批量删除(${selectedRowKeys.length})`}
            >
              {isMobile ? selectedRowKeys.length.toString() : `批量删除(${selectedRowKeys.length})`}
            </Button>
            <Button
              size="small"
              icon={<ReloadOutlined />}
              onClick={fetchAPIKeys}
              title="刷新"
            >
              {isMobile ? '' : '刷新'}
            </Button>
            <Button
              size="small"
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => openModal()}
              title="添加API Key"
            >
              {isMobile ? '' : '添加API Key'}
            </Button>
            </Space>
          </div>
        </div>
      </Card>

      <div style={{
        marginBottom: 16,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        gap: '12px'
      }}>
        <Input.Search
          placeholder="搜索名称或API Key..."
          allowClear
          style={{
            flex: 1,
            maxWidth: '300px'
          }}
          prefix={<SearchOutlined />}
          value={searchText}
          onChange={(e) => setSearchText(e.target.value)}
        />
        <Dropdown
          menu={{ items: columnMenuItems }}
          trigger={['click']}
          placement="bottomRight"
        >
          <Button
            size="small"
            icon={<SettingOutlined />}
            title="列设置"
          >
            {isMobile ? '' : '列设置'}
          </Button>
        </Dropdown>
      </div>

      <Table
        columns={visibleColumns}
        dataSource={filteredApiKeys}
        loading={loading}
        rowKey="id"
        rowSelection={{
          selectedRowKeys,
          onChange: (keys) => setSelectedRowKeys(keys as number[]),
          selections: [
            Table.SELECTION_ALL,
            Table.SELECTION_INVERT,
            Table.SELECTION_NONE,
          ],
        }}
        pagination={paginationConfig}
        scroll={{ x: 1200 }}
      />

      <Modal
        title={editingKey ? '编辑API Key' : '添加API Key'}
        open={modalVisible}
        onCancel={closeModal}
        footer={null}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
        >
          {!editingKey && (
            <Form.Item
              name="is_batch"
              valuePropName="checked"
            >
              <Checkbox>批量添加</Checkbox>
            </Form.Item>
          )}

          <Form.Item
            name="name"
            label="名称"
            rules={[{ required: true, message: '请输入API Key名称' }]}
          >
            <Input placeholder="请输入API Key名称" />
          </Form.Item>



          <Form.Item
            name="provider"
            label="提供商"
            rules={[{ required: true, message: '请选择提供商' }]}
          >
            <Select placeholder="请选择提供商" showSearch>
              {providers.map(provider => (
                <Option key={provider.code} value={provider.code}>
                  {provider.name}
                </Option>
              ))}
            </Select>
          </Form.Item>



          <Form.Item noStyle dependencies={['is_batch']}>
            {({ getFieldValue }) => {
              const isBatch = getFieldValue('is_batch')
              return (
                <Form.Item
                  name="api_key"
                  label="API Key"
                  rules={[
                    { required: !editingKey, message: '请输入API Key' },
                    { min: 10, message: 'API Key长度至少10位' }
                  ]}
                >
                  {isBatch ? (
                    <Input.TextArea
                      placeholder="请输入多个API Key，用逗号分隔&#10;sk-abc123...,sk-def456...,sk-ghi789..."
                      rows={4}
                      style={{ fontFamily: 'monospace' }}
                    />
                  ) : (
                    <Input
                      placeholder={editingKey ? '留空则不修改' : '请输入API Key'}
                      style={{ fontFamily: 'monospace' }}
                    />
                  )}
                </Form.Item>
              )
            }}
          </Form.Item>

          <Form.Item
            name="description"
            label="描述"
          >
            <Input.TextArea
              placeholder="请输入描述信息（可选）"
              rows={3}
            />
          </Form.Item>



          <Form.Item dependencies={['is_batch']}>
            {({ getFieldValue }) => {
              const isBatch = getFieldValue('is_batch')
              return (
                <Space>
                  <Button type="primary" htmlType="submit">
                    {editingKey ? '更新' : (isBatch ? '批量创建' : '创建')}
                  </Button>
                  <Button onClick={closeModal}>
                    取消
                  </Button>
                </Space>
              )
            }}
          </Form.Item>
        </Form>
      </Modal>
    </div>
  )
}

export default APIKeys
