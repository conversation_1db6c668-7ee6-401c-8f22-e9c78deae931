package database

import (
	"keyhub/internal/models"

	"github.com/glebarez/sqlite"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

// Initialize 初始化数据库连接
func Initialize(databaseURL string) (*gorm.DB, error) {
	// 使用文件数据库以确保数据持久化
	// 移除强制使用内存数据库的逻辑

	db, err := gorm.Open(sqlite.Open(databaseURL), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Silent), // 禁用SQL日志输出
	})
	if err != nil {
		return nil, err
	}

	// 自动迁移数据库表
	err = db.AutoMigrate(
		&models.APIKey{},
		&models.TestResult{},
		&models.BalanceRecord{},
		&models.Task{},
	)
	if err != nil {
		return nil, err
	}

	return db, nil
}
