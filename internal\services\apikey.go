package services

import (
	"fmt"
	"strings"
	"time"

	"keyhub/internal/models"

	"gorm.io/gorm"
)

type APIKeyService struct {
	db *gorm.DB
}

func NewAPIKeyService(db *gorm.DB) *APIKeyService {
	return &APIKeyService{db: db}
}



// GetAll 获取所有API Key
func (s *APIKeyService) GetAll() ([]models.APIKey, error) {
	var apiKeys []models.APIKey
	err := s.db.Find(&apiKeys).Error
	if err != nil {
		return nil, err
	}

	// 隐藏API Key的敏感部分（只显示前几位）
	for i := range apiKeys {
		if len(apiKeys[i].APIKey) > 10 {
			apiKeys[i].APIKey = apiKeys[i].APIKey[:10] + "..."
		}
	}

	return apiKeys, nil
}

// Create 创建新的API Key
func (s *APIKeyService) Create(apiKey *models.APIKey) error {
	apiKey.CreatedAt = time.Now()
	apiKey.UpdatedAt = time.Now()

	return s.db.Create(apiKey).Error
}

// GetByID 根据ID获取API Key
func (s *APIKeyService) GetByID(id uint) (*models.APIKey, error) {
	var apiKey models.APIKey
	err := s.db.First(&apiKey, id).Error
	if err != nil {
		return nil, err
	}

	return &apiKey, nil
}

// Update 更新API Key
func (s *APIKeyService) Update(apiKey *models.APIKey) error {
	apiKey.UpdatedAt = time.Now()
	return s.db.Save(apiKey).Error
}

// Delete 删除API Key
func (s *APIKeyService) Delete(id uint) error {
	return s.db.Delete(&models.APIKey{}, id).Error
}

// DeleteBatch 批量删除API Key
func (s *APIKeyService) DeleteBatch(ids []uint) (int, error) {
	if len(ids) == 0 {
		return 0, fmt.Errorf("没有提供要删除的ID")
	}

	// 检查要删除的API Key是否存在
	var existingKeys []models.APIKey
	err := s.db.Where("id IN ?", ids).Find(&existingKeys).Error
	if err != nil {
		return 0, fmt.Errorf("查询API Key失败: %v", err)
	}

	if len(existingKeys) == 0 {
		return 0, fmt.Errorf("没有找到要删除的API Key")
	}

	// 执行批量删除
	result := s.db.Where("id IN ?", ids).Delete(&models.APIKey{})
	if result.Error != nil {
		return 0, fmt.Errorf("批量删除失败: %v", result.Error)
	}

	return int(result.RowsAffected), nil
}

// GetRawAPIKey 获取原始API Key（用于实际API调用）
func (s *APIKeyService) GetRawAPIKey(id uint) (string, error) {
	var apiKey models.APIKey
	err := s.db.First(&apiKey, id).Error
	if err != nil {
		return "", err
	}

	return apiKey.APIKey, nil
}

// BatchCreateResult 批量创建结果
type BatchCreateResult struct {
	CreatedKeys   []models.APIKey `json:"created_keys"`
	SkippedKeys   []string        `json:"skipped_keys"`
	InvalidKeys   []string        `json:"invalid_keys"`
	TotalInput    int             `json:"total_input"`
	CreatedCount  int             `json:"created_count"`
	SkippedCount  int             `json:"skipped_count"`
	InvalidCount  int             `json:"invalid_count"`
}

// ParseAndCreateBatch 解析并批量创建API Key
func (s *APIKeyService) ParseAndCreateBatch(baseName, provider, apiKeysStr, description string) (*BatchCreateResult, error) {
	// 分割API Key字符串
	keyStrings := strings.Split(apiKeysStr, ",")

	result := &BatchCreateResult{
		CreatedKeys: make([]models.APIKey, 0),
		SkippedKeys: make([]string, 0),
		InvalidKeys: make([]string, 0),
		TotalInput:  len(keyStrings),
	}

	var validKeys []string

	// 清理和验证API Key
	for _, keyStr := range keyStrings {
		key := strings.TrimSpace(keyStr)
		if key == "" {
			continue // 跳过空字符串
		}
		if len(key) < 10 {
			result.InvalidKeys = append(result.InvalidKeys, key)
			continue
		}
		validKeys = append(validKeys, key)
	}

	if len(validKeys) == 0 {
		result.InvalidCount = len(result.InvalidKeys)
		return result, fmt.Errorf("没有找到有效的API Key")
	}

	// 检查重复并分离出可创建的Key
	var keysToCreate []string
	for _, key := range validKeys {
		var count int64
		err := s.db.Model(&models.APIKey{}).Where("api_key = ?", key).Count(&count).Error
		if err != nil {
			return nil, fmt.Errorf("检查API Key重复时出错: %v", err)
		}
		if count > 0 {
			// 重复的Key，添加到跳过列表
			result.SkippedKeys = append(result.SkippedKeys, key[:10]+"...")
		} else {
			// 可以创建的Key
			keysToCreate = append(keysToCreate, key)
		}
	}

	// 批量创建API Key
	now := time.Now()
	for i, key := range keysToCreate {
		// 生成名称：如果只有一个key，使用原名称；多个key则添加序号
		name := baseName
		if len(validKeys) > 1 {
			// 计算实际的序号（考虑跳过的Key）
			actualIndex := 1
			for j := 0; j <= i; j++ {
				// 找到当前key在原始validKeys中的位置
				for k, originalKey := range validKeys {
					if originalKey == key {
						actualIndex = k + 1
						break
					}
				}
			}
			name = fmt.Sprintf("%s-%d", baseName, actualIndex)
		}

		apiKey := models.APIKey{
			Name:        name,
			Provider:    provider,
			APIKey:      key,
			Description: description,
			CreatedAt:   now,
			UpdatedAt:   now,
		}

		if err := s.db.Create(&apiKey).Error; err != nil {
			return nil, fmt.Errorf("创建API Key '%s' 失败: %v", name, err)
		}

		// 隐藏API Key用于返回
		if len(apiKey.APIKey) > 10 {
			apiKey.APIKey = apiKey.APIKey[:10] + "..."
		}
		result.CreatedKeys = append(result.CreatedKeys, apiKey)
	}

	// 更新统计信息
	result.CreatedCount = len(result.CreatedKeys)
	result.SkippedCount = len(result.SkippedKeys)
	result.InvalidCount = len(result.InvalidKeys)

	return result, nil
}
