package services

import (
	"context"
	"fmt"
	"log"
	"sync"
	"time"

	"keyhub/internal/models"

	"gorm.io/gorm"
)

// QueueService 队列服务
type QueueService struct {
	db       *gorm.DB
	workers  map[string]*Worker
	mu       sync.RWMutex
	ctx      context.Context
	cancel   context.CancelFunc
	wg       sync.WaitGroup
}

// Worker 工作器
type Worker struct {
	ID       string
	queue    *QueueService
	ctx      context.Context
	cancel   context.CancelFunc
	taskChan chan *models.Task
}

// NewQueueService 创建队列服务
func NewQueueService(db *gorm.DB) *QueueService {
	ctx, cancel := context.WithCancel(context.Background())
	return &QueueService{
		db:      db,
		workers: make(map[string]*Worker),
		ctx:     ctx,
		cancel:  cancel,
	}
}

// Start 启动队列服务
func (qs *QueueService) Start(workerCount int) {
	log.Printf("启动队列服务，工作器数量: %d", workerCount)
	
	// 启动工作器
	for i := 0; i < workerCount; i++ {
		workerID := fmt.Sprintf("worker-%d", i+1)
		worker := qs.createWorker(workerID)
		qs.mu.Lock()
		qs.workers[workerID] = worker
		qs.mu.Unlock()
		
		qs.wg.Add(1)
		go worker.run()
	}
	
	// 启动任务调度器
	qs.wg.Add(1)
	go qs.scheduler()
	
	// 启动重试处理器
	qs.wg.Add(1)
	go qs.retryHandler()
}

// Stop 停止队列服务
func (qs *QueueService) Stop() {
	log.Println("停止队列服务...")
	qs.cancel()
	qs.wg.Wait()
	log.Println("队列服务已停止")
}

// EnqueueTest 添加测试任务到队列
func (qs *QueueService) EnqueueTest(apiKeyID uint, testType, model string, priority int) (*models.Task, error) {
	// 检查是否已存在相同API Key的pending或running测试任务
	existingTask, err := qs.findExistingTestTask(apiKeyID, testType)
	if err != nil {
		return nil, fmt.Errorf("检查重复任务失败: %v", err)
	}

	if existingTask != nil {
		return existingTask, nil // 返回现有任务而不是创建新任务
	}

	payload := models.TestTaskPayload{
		APIKeyID: apiKeyID,
		TestType: testType,
		Model:    model,
	}

	task := &models.Task{
		Type:       models.TaskTypeTest,
		Status:     models.TaskStatusPending,
		Priority:   priority,
		MaxRetries: 3,
		CreatedAt:  time.Now(),
		UpdatedAt:  time.Now(),
	}

	if err := task.SetPayload(payload); err != nil {
		return nil, fmt.Errorf("设置任务参数失败: %v", err)
	}

	if err := qs.db.Create(task).Error; err != nil {
		return nil, fmt.Errorf("创建任务失败: %v", err)
	}

	log.Printf("测试任务已入队: ID=%d, APIKeyID=%d, Type=%s", task.ID, apiKeyID, testType)
	return task, nil
}

// findExistingTestTask 查找是否存在相同API Key的pending或running测试任务
func (qs *QueueService) findExistingTestTask(apiKeyID uint, testType string) (*models.Task, error) {
	var task models.Task

	// 查询相同API Key的pending或running测试任务
	err := qs.db.Where("type = ? AND status IN (?, ?) AND JSON_EXTRACT(payload, '$.api_key_id') = ? AND JSON_EXTRACT(payload, '$.test_type') = ?",
		models.TaskTypeTest,
		models.TaskStatusPending,
		models.TaskStatusRunning,
		apiKeyID,
		testType,
	).First(&task).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil // 没有找到重复任务
		}
		return nil, err
	}

	return &task, nil
}

// findExistingBalanceTask 查找是否存在相同API Key的pending或running余额查询任务
func (qs *QueueService) findExistingBalanceTask(apiKeyID uint) (*models.Task, error) {
	var task models.Task

	// 查询相同API Key的pending或running余额查询任务
	err := qs.db.Where("type = ? AND status IN (?, ?) AND JSON_EXTRACT(payload, '$.api_key_id') = ?",
		models.TaskTypeBalance,
		models.TaskStatusPending,
		models.TaskStatusRunning,
		apiKeyID,
	).First(&task).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil // 没有找到重复任务
		}
		return nil, err
	}

	return &task, nil
}

// EnqueueBalance 添加余额查询任务到队列
func (qs *QueueService) EnqueueBalance(apiKeyID uint, priority int) (*models.Task, error) {
	// 检查是否已存在相同API Key的pending或running余额查询任务
	existingTask, err := qs.findExistingBalanceTask(apiKeyID)
	if err != nil {
		return nil, fmt.Errorf("检查重复任务失败: %v", err)
	}

	if existingTask != nil {
		return existingTask, nil // 返回现有任务而不是创建新任务
	}

	payload := models.BalanceTaskPayload{
		APIKeyID: apiKeyID,
	}

	task := &models.Task{
		Type:       models.TaskTypeBalance,
		Status:     models.TaskStatusPending,
		Priority:   priority,
		MaxRetries: 3,
		CreatedAt:  time.Now(),
		UpdatedAt:  time.Now(),
	}

	if err := task.SetPayload(payload); err != nil {
		return nil, fmt.Errorf("设置任务参数失败: %v", err)
	}

	if err := qs.db.Create(task).Error; err != nil {
		return nil, fmt.Errorf("创建任务失败: %v", err)
	}

	log.Printf("余额查询任务已入队: ID=%d, APIKeyID=%d", task.ID, apiKeyID)
	return task, nil
}

// EnqueueBatchTest 添加批量测试任务到队列
func (qs *QueueService) EnqueueBatchTest(apiKeyIDs []uint, testType, model string, priority int) ([]*models.Task, error) {
	var tasks []*models.Task
	
	for _, apiKeyID := range apiKeyIDs {
		task, err := qs.EnqueueTest(apiKeyID, testType, model, priority)
		if err != nil {
			return nil, fmt.Errorf("创建批量测试任务失败: %v", err)
		}
		tasks = append(tasks, task)
	}
	
	return tasks, nil
}

// EnqueueBatchBalance 添加批量余额查询任务到队列
func (qs *QueueService) EnqueueBatchBalance(apiKeyIDs []uint, priority int) ([]*models.Task, error) {
	var tasks []*models.Task
	
	for _, apiKeyID := range apiKeyIDs {
		task, err := qs.EnqueueBalance(apiKeyID, priority)
		if err != nil {
			return nil, fmt.Errorf("创建批量余额查询任务失败: %v", err)
		}
		tasks = append(tasks, task)
	}
	
	return tasks, nil
}

// GetTaskStatus 获取任务状态
func (qs *QueueService) GetTaskStatus(taskID uint) (*models.Task, error) {
	var task models.Task
	err := qs.db.First(&task, taskID).Error
	return &task, err
}

// GetTasksByStatus 根据状态获取任务列表
func (qs *QueueService) GetTasksByStatus(status models.TaskStatus, limit int) ([]models.Task, error) {
	var tasks []models.Task
	query := qs.db.Where("status = ?", status).Order("priority DESC, created_at ASC")
	if limit > 0 {
		query = query.Limit(limit)
	}
	err := query.Find(&tasks).Error
	return tasks, err
}

// CancelTask 取消任务
func (qs *QueueService) CancelTask(taskID uint) error {
	return qs.db.Model(&models.Task{}).Where("id = ? AND status IN (?)", taskID, []models.TaskStatus{
		models.TaskStatusPending,
	}).Update("status", models.TaskStatusCancelled).Error
}

// createWorker 创建工作器
func (qs *QueueService) createWorker(workerID string) *Worker {
	ctx, cancel := context.WithCancel(qs.ctx)
	return &Worker{
		ID:       workerID,
		queue:    qs,
		ctx:      ctx,
		cancel:   cancel,
		taskChan: make(chan *models.Task, 10),
	}
}

// scheduler 任务调度器
func (qs *QueueService) scheduler() {
	defer qs.wg.Done()
	ticker := time.NewTicker(1 * time.Second) // 每秒检查一次
	defer ticker.Stop()
	
	for {
		select {
		case <-qs.ctx.Done():
			return
		case <-ticker.C:
			qs.dispatchTasks()
		}
	}
}

// dispatchTasks 分发任务给工作器
func (qs *QueueService) dispatchTasks() {
	// 获取待执行的任务
	tasks, err := qs.GetTasksByStatus(models.TaskStatusPending, 10)
	if err != nil {
		log.Printf("获取待执行任务失败: %v", err)
		return
	}
	
	if len(tasks) == 0 {
		return
	}
	
	// 分发任务给空闲的工作器
	qs.mu.RLock()
	defer qs.mu.RUnlock()
	
	for i, task := range tasks {
		if i >= len(qs.workers) {
			break // 没有更多工作器了
		}
		
		// 找到一个工作器
		for _, worker := range qs.workers {
			select {
			case worker.taskChan <- &task:
				// 任务已发送给工作器
				goto nextTask
			default:
				// 工作器忙碌，尝试下一个
				continue
			}
		}
		
		nextTask:
	}
}

// retryHandler 重试处理器
func (qs *QueueService) retryHandler() {
	defer qs.wg.Done()
	ticker := time.NewTicker(30 * time.Second) // 每30秒检查一次重试
	defer ticker.Stop()
	
	for {
		select {
		case <-qs.ctx.Done():
			return
		case <-ticker.C:
			qs.processRetries()
		}
	}
}

// processRetries 处理重试任务
func (qs *QueueService) processRetries() {
	var tasks []models.Task
	err := qs.db.Where("status = ? AND retry_count < max_retries AND (next_retry_at IS NULL OR next_retry_at <= ?)",
		models.TaskStatusFailed, time.Now()).Find(&tasks).Error
	if err != nil {
		log.Printf("获取重试任务失败: %v", err)
		return
	}

	for _, task := range tasks {
		// 重置任务状态为待执行
		err := qs.db.Model(&task).Updates(map[string]interface{}{
			"status":     models.TaskStatusPending,
			"worker_id":  "",
			"started_at": nil,
			"error_msg":  "",
		}).Error
		if err != nil {
			log.Printf("重置重试任务失败: %v", err)
			continue
		}
		log.Printf("任务 %d 已重新入队等待重试", task.ID)
	}
}

// Worker 方法

// run 工作器运行循环
func (w *Worker) run() {
	defer w.queue.wg.Done()
	log.Printf("工作器 %s 已启动", w.ID)

	for {
		select {
		case <-w.ctx.Done():
			log.Printf("工作器 %s 已停止", w.ID)
			return
		case task := <-w.taskChan:
			w.processTask(task)
		}
	}
}

// processTask 处理任务
func (w *Worker) processTask(task *models.Task) {

	// 更新任务状态为运行中
	now := time.Now()
	err := w.queue.db.Model(task).Updates(map[string]interface{}{
		"status":     models.TaskStatusRunning,
		"worker_id":  w.ID,
		"started_at": &now,
	}).Error
	if err != nil {
		log.Printf("更新任务状态失败: %v", err)
		return
	}

	// 执行任务
	var result interface{}
	var execErr error

	switch task.Type {
	case models.TaskTypeTest:
		result, execErr = w.executeTestTask(task)
	case models.TaskTypeBalance:
		result, execErr = w.executeBalanceTask(task)
	default:
		execErr = fmt.Errorf("未知的任务类型: %s", task.Type)
	}

	// 更新任务结果
	w.updateTaskResult(task, result, execErr)
}

// executeTestTask 执行测试任务
func (w *Worker) executeTestTask(task *models.Task) (interface{}, error) {
	var payload models.TestTaskPayload
	if err := task.GetPayload(&payload); err != nil {
		return nil, fmt.Errorf("解析任务参数失败: %v", err)
	}

	// 创建测试服务
	testService := NewTestService(w.queue.db)

	// 执行测试
	testResult, err := testService.TestSingleWithModel(payload.APIKeyID, payload.TestType, payload.Model)
	if err != nil {
		return nil, fmt.Errorf("执行测试失败: %v", err)
	}

	// 构建任务结果
	result := models.TestTaskResult{
		Success:      testResult.Status == "success",
		ResponseTime: testResult.ResponseTime,
		ErrorMessage: testResult.ErrorMessage,
	}

	return result, nil
}

// executeBalanceTask 执行余额查询任务
func (w *Worker) executeBalanceTask(task *models.Task) (interface{}, error) {
	var payload models.BalanceTaskPayload
	if err := task.GetPayload(&payload); err != nil {
		return nil, fmt.Errorf("解析任务参数失败: %v", err)
	}

	// 创建余额服务
	balanceService := NewBalanceService(w.queue.db)

	// 执行余额查询
	balanceRecord, err := balanceService.CheckBalance(payload.APIKeyID)
	if err != nil {
		return nil, fmt.Errorf("执行余额查询失败: %v", err)
	}

	// 构建任务结果
	result := models.BalanceTaskResult{
		Success:      true,
		TotalBalance: balanceRecord.TotalBalance,
		GrantBalance: balanceRecord.GrantBalance,
		PaidBalance:  balanceRecord.PaidBalance,
		Currency:     balanceRecord.Currency,
	}

	return result, nil
}

// updateTaskResult 更新任务结果
func (w *Worker) updateTaskResult(task *models.Task, result interface{}, execErr error) {
	now := time.Now()
	updates := map[string]interface{}{
		"completed_at": &now,
		"updated_at":   now,
	}

	if execErr != nil {
		// 任务执行失败
		updates["status"] = models.TaskStatusFailed
		updates["error_msg"] = execErr.Error()
		updates["retry_count"] = task.RetryCount + 1

		// 如果还可以重试，设置下次重试时间
		if task.RetryCount+1 < task.MaxRetries {
			nextRetry := task.CalculateNextRetryTime()
			updates["next_retry_at"] = &nextRetry
		}

		log.Printf("任务 %d 执行失败: %v (重试次数: %d/%d)", task.ID, execErr, task.RetryCount+1, task.MaxRetries)
	} else {
		// 任务执行成功
		updates["status"] = models.TaskStatusCompleted

		// 保存结果
		if result != nil {
			if err := task.SetResult(result); err != nil {
				log.Printf("保存任务结果失败: %v", err)
			} else {
				updates["result"] = task.Result
			}
		}


	}

	// 更新数据库
	if err := w.queue.db.Model(task).Updates(updates).Error; err != nil {
		log.Printf("更新任务结果失败: %v", err)
	}
}
