package services

import (
	"fmt"
	"time"

	"keyhub/internal/config"
	"keyhub/internal/models"

	"gorm.io/gorm"
)

type TestService struct {
	db         *gorm.DB
	chatTester ChatTester
}

func NewTestService(db *gorm.DB) *TestService {
	return &TestService{
		db:         db,
		chatTester: NewDefaultChatTester(),
	}
}

// TestSingleWithModel 使用指定模型测试单个API Key
func (s *TestService) TestSingleWithModel(apiKeyID uint, testType string, model string) (*models.TestResult, error) {
	// 获取API Key信息
	var apiKey models.APIKey
	if err := s.db.First(&apiKey, apiKeyID).Error; err != nil {
		return nil, fmt.Errorf("API Key不存在: %v", err)
	}

	// 获取提供商信息
	provider := config.GetProviderByCode(apiKey.Provider)
	if provider == nil {
		return nil, fmt.Errorf("不支持的提供商: %s", apiKey.Provider)
	}

	// 检查测试类型
	if testType != "chat" {
		return nil, fmt.Errorf("不支持的测试类型: %s", testType)
	}

	// 检查是否支持聊天测试
	if !provider.SupportChat {
		result := &models.TestResult{
			APIKeyID:     apiKeyID,
			TestType:     testType,
			Status:       "failed",
			ResponseTime: 0,
			ErrorMessage: fmt.Sprintf("提供商 %s 不支持聊天测试", provider.Name),
			CreatedAt:    time.Now(),
		}
		s.db.Create(result)

		// 更新APIKey表中的测试状态
		now := time.Now()
		testStatus := false
		updateData := map[string]interface{}{
			"last_test_status":   &testStatus,
			"last_test_time":     &now,
			"last_response_time": 0,
			"last_test_error":    result.ErrorMessage,
			"updated_at":         now,
		}
		s.db.Model(&models.APIKey{}).Where("id = ?", apiKeyID).Updates(updateData)

		return result, nil
	}

	// 获取原始API Key
	apiKeyService := NewAPIKeyService(s.db)
	rawAPIKey, err := apiKeyService.GetRawAPIKey(apiKeyID)
	if err != nil {
		return nil, fmt.Errorf("获取API Key失败: %v", err)
	}

	// 执行聊天测试
	chatResult, err := s.chatTester.TestChat(rawAPIKey, provider, model)
	if err != nil {
		result := &models.TestResult{
			APIKeyID:     apiKeyID,
			TestType:     testType,
			Status:       "failed",
			ResponseTime: 0,
			ErrorMessage: fmt.Sprintf("测试执行失败: %v", err),
			CreatedAt:    time.Now(),
		}
		s.db.Create(result)

		// 更新APIKey表中的测试状态
		now := time.Now()
		testStatus := false
		updateData := map[string]interface{}{
			"last_test_status":   &testStatus,
			"last_test_time":     &now,
			"last_response_time": 0,
			"last_test_error":    result.ErrorMessage,
			"updated_at":         now,
		}
		s.db.Model(&models.APIKey{}).Where("id = ?", apiKeyID).Updates(updateData)

		return result, nil
	}

	// 创建测试结果记录
	status := "failed"
	testStatus := false
	if chatResult.Success {
		status = "success"
		testStatus = true
	}

	result := &models.TestResult{
		APIKeyID:     apiKeyID,
		TestType:     testType,
		Status:       status,
		ResponseTime: chatResult.ResponseTime,
		ErrorMessage: chatResult.ErrorMessage,
		CreatedAt:    time.Now(),
	}

	// 保存测试结果到TestResult表
	err = s.db.Create(result).Error
	if err != nil {
		return result, err
	}

	// 更新APIKey表中的最新测试信息
	now := time.Now()
	updateData := map[string]interface{}{
		"last_test_status":   &testStatus,
		"last_test_time":     &now,
		"last_response_time": chatResult.ResponseTime,
		"last_test_error":    chatResult.ErrorMessage,
		"updated_at":         now,
	}

	err = s.db.Model(&models.APIKey{}).Where("id = ?", apiKeyID).Updates(updateData).Error
	return result, err
}

// GetResults 获取测试结果
func (s *TestService) GetResults() ([]models.TestResult, error) {
	var results []models.TestResult
	err := s.db.Preload("APIKey").Find(&results).Error
	return results, err
}
