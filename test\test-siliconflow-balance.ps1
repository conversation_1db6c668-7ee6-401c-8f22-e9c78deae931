# KeyHub SiliconFlow Balance Test Script
Write-Host "========================================"
Write-Host "SiliconFlow Balance Query Test"
Write-Host "========================================"
Write-Host ""

Write-Host "1. Creating SiliconFlow API Key test data..."
$createData = @{
    name = "SiliconFlow Test Key"
    provider = "siliconflow"
    api_key = "sk-test123456789"
    description = "Test key for balance query"
} | ConvertTo-Json

try {
    $response = Invoke-RestMethod -Uri "http://localhost:8888/api/v1/apikeys" -Method POST -Body $createData -ContentType "application/json"
    Write-Host "✅ API Key created successfully" -ForegroundColor Green
    Write-Host ($response | ConvertTo-Json -Depth 3)
} catch {
    Write-Host "❌ Failed to create API Key: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host ""

Write-Host "2. Getting all API Keys..."
try {
    $apiKeys = Invoke-RestMethod -Uri "http://localhost:8888/api/v1/apikeys" -Method GET
    Write-Host "✅ API Keys retrieved successfully" -ForegroundColor Green
    Write-Host ($apiKeys | ConvertTo-Json -Depth 3)
} catch {
    Write-Host "❌ Failed to get API Keys: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host ""

Write-Host "3. Testing balance query (using ID=1, adjust if needed)..."
Write-Host "Note: This will call real SiliconFlow API, ensure API Key is valid"
try {
    $balancePayload = @{ api_key_id = 1 } | ConvertTo-Json
    $balance = Invoke-RestMethod -Uri "http://localhost:8888/api/v1/balance" -Method POST -Body $balancePayload -ContentType "application/json"
    Write-Host "✅ Balance query successful" -ForegroundColor Green
    Write-Host ($balance | ConvertTo-Json -Depth 3)
} catch {
    Write-Host "❌ Balance query failed: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host ""

Write-Host "4. Getting balance history..."
try {
    $history = Invoke-RestMethod -Uri "http://localhost:8888/api/v1/balance/history/1" -Method GET
    Write-Host "✅ Balance history retrieved" -ForegroundColor Green
    Write-Host ($history | ConvertTo-Json -Depth 3)
} catch {
    Write-Host "❌ Failed to get balance history: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host ""
Write-Host "Test completed!"
Write-Host ""
Write-Host "If you see errors, please check:"
Write-Host "1. Backend service is running (http://localhost:8888)"
Write-Host "2. API Key is valid"
Write-Host "3. Network connection is working"
Write-Host ""
Read-Host "Press Enter to continue"
