import { useState, useCallback } from 'react'

export const useSetState = <T>(initialValue: Set<T> = new Set<T>()) => {
  const [state, setState] = useState<Set<T>>(initialValue)

  const add = useCallback((item: T) => {
    setState(prev => new Set(prev).add(item))
  }, [])

  const remove = useCallback((item: T) => {
    setState(prev => {
      const newSet = new Set(prev)
      newSet.delete(item)
      return newSet
    })
  }, [])

  const toggle = useCallback((item: T) => {
    setState(prev => {
      const newSet = new Set(prev)
      if (newSet.has(item)) {
        newSet.delete(item)
      } else {
        newSet.add(item)
      }
      return newSet
    })
  }, [])

  const clear = useCallback(() => {
    setState(new Set<T>())
  }, [])

  const has = useCallback((item: T) => {
    return state.has(item)
  }, [state])

  return {
    state,
    setState,
    add,
    remove,
    toggle,
    clear,
    has
  }
}
