package models

import (
	"encoding/json"
	"time"
)

// TaskStatus 任务状态枚举
type TaskStatus string

const (
	TaskStatusPending    TaskStatus = "pending"    // 等待执行
	TaskStatusRunning    TaskStatus = "running"    // 正在执行
	TaskStatusCompleted  TaskStatus = "completed"  // 已完成
	TaskStatusFailed     TaskStatus = "failed"     // 执行失败
	TaskStatusCancelled  TaskStatus = "cancelled"  // 已取消
)

// TaskType 任务类型枚举
type TaskType string

const (
	TaskTypeTest    TaskType = "test"    // 测试任务
	TaskTypeBalance TaskType = "balance" // 余额查询任务
)

// Task 任务模型
type Task struct {
	ID          uint       `json:"id" gorm:"primary_key"`
	Type        TaskType   `json:"type" gorm:"not null"`        // 任务类型
	Status      TaskStatus `json:"status" gorm:"default:'pending'"` // 任务状态
	Priority    int        `json:"priority" gorm:"default:0"`   // 优先级，数字越大优先级越高
	
	// 任务参数（JSON格式存储）
	Payload     string     `json:"payload" gorm:"type:text"`    // 任务参数
	
	// 执行信息
	WorkerID    string     `json:"worker_id"`                   // 执行该任务的工作器ID
	StartedAt   *time.Time `json:"started_at"`                  // 开始执行时间
	CompletedAt *time.Time `json:"completed_at"`                // 完成时间
	
	// 结果信息
	Result      string     `json:"result" gorm:"type:text"`     // 执行结果（JSON格式）
	ErrorMsg    string     `json:"error_msg" gorm:"type:text"`  // 错误信息
	
	// 重试信息
	RetryCount  int        `json:"retry_count" gorm:"default:0"` // 重试次数
	MaxRetries  int        `json:"max_retries" gorm:"default:3"` // 最大重试次数
	NextRetryAt *time.Time `json:"next_retry_at"`               // 下次重试时间
	
	// 时间戳
	CreatedAt   time.Time  `json:"created_at"`
	UpdatedAt   time.Time  `json:"updated_at"`
}

// TestTaskPayload 测试任务的参数结构
type TestTaskPayload struct {
	APIKeyID  uint   `json:"api_key_id"`
	TestType  string `json:"test_type"`
	Model     string `json:"model,omitempty"`
}

// BalanceTaskPayload 余额查询任务的参数结构
type BalanceTaskPayload struct {
	APIKeyID uint `json:"api_key_id"`
}

// BatchTaskPayload 批量任务的参数结构
type BatchTaskPayload struct {
	APIKeyIDs []uint `json:"api_key_ids"`
	TestType  string `json:"test_type,omitempty"`
	Model     string `json:"model,omitempty"`
}

// TestTaskResult 测试任务的结果结构
type TestTaskResult struct {
	Success      bool   `json:"success"`
	ResponseTime int    `json:"response_time"`
	ErrorMessage string `json:"error_message,omitempty"`
	Response     string `json:"response,omitempty"`
	Model        string `json:"model,omitempty"`
}

// BalanceTaskResult 余额查询任务的结果结构
type BalanceTaskResult struct {
	Success      bool    `json:"success"`
	TotalBalance float64 `json:"total_balance"`
	GrantBalance float64 `json:"grant_balance"`
	PaidBalance  float64 `json:"paid_balance"`
	Currency     string  `json:"currency"`
	ErrorMessage string  `json:"error_message,omitempty"`
}

// SetPayload 设置任务参数
func (t *Task) SetPayload(payload interface{}) error {
	data, err := json.Marshal(payload)
	if err != nil {
		return err
	}
	t.Payload = string(data)
	return nil
}

// GetPayload 获取任务参数
func (t *Task) GetPayload(payload interface{}) error {
	return json.Unmarshal([]byte(t.Payload), payload)
}

// SetResult 设置任务结果
func (t *Task) SetResult(result interface{}) error {
	data, err := json.Marshal(result)
	if err != nil {
		return err
	}
	t.Result = string(data)
	return nil
}

// GetResult 获取任务结果
func (t *Task) GetResult(result interface{}) error {
	if t.Result == "" {
		return nil
	}
	return json.Unmarshal([]byte(t.Result), result)
}

// IsRetryable 判断任务是否可以重试
func (t *Task) IsRetryable() bool {
	return t.Status == TaskStatusFailed && t.RetryCount < t.MaxRetries
}

// ShouldRetry 判断是否应该重试（检查重试时间）
func (t *Task) ShouldRetry() bool {
	if !t.IsRetryable() {
		return false
	}
	if t.NextRetryAt == nil {
		return true
	}
	return time.Now().After(*t.NextRetryAt)
}

// CalculateNextRetryTime 计算下次重试时间（指数退避）
func (t *Task) CalculateNextRetryTime() time.Time {
	// 指数退避：2^retry_count 分钟
	backoffMinutes := 1 << t.RetryCount // 1, 2, 4, 8 分钟
	if backoffMinutes > 60 {
		backoffMinutes = 60 // 最大1小时
	}
	return time.Now().Add(time.Duration(backoffMinutes) * time.Minute)
}
