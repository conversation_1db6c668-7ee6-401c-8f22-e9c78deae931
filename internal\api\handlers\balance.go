package handlers

import (
	"strconv"
	"net/http"

	"keyhub/internal/services"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type BalanceHandler struct {
	service      *services.BalanceService
	queueService *services.QueueService
}

func NewBalanceHandler(db *gorm.DB, queueService *services.QueueService) *BalanceHandler {
	return &BalanceHandler{
		service:      services.NewBalanceService(db),
		queueService: queueService,
	}
}

// Check 统一余额查询接口（支持单个和批量）
func (h *BalanceHandler) Check(c *gin.Context) {
	var request struct {
		APIKeyID  *uint  `json:"api_key_id,omitempty"`  // 单个查询时使用
		APIKeyIDs []uint `json:"api_key_ids,omitempty"` // 批量查询时使用
		Priority  int    `json:"priority"`              // 可选，任务优先级
	}

	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// 设置默认优先级
	if request.Priority == 0 {
		request.Priority = 5 // 中等优先级
	}

	// 判断是单个查询还是批量查询
	if request.APIKeyID != nil {
		// 单个查询
		task, err := h.queueService.EnqueueBalance(*request.APIKeyID, request.Priority)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}

		c.JSON(http.StatusAccepted, gin.H{
			"message": "余额查询任务已添加到队列",
			"task_id": task.ID,
			"status":  task.Status,
			"count":   1,
		})
	} else if len(request.APIKeyIDs) > 0 {
		// 批量查询
		tasks, err := h.queueService.EnqueueBatchBalance(request.APIKeyIDs, request.Priority)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}

		// 提取任务ID
		taskIDs := make([]uint, len(tasks))
		for i, task := range tasks {
			taskIDs[i] = task.ID
		}

		c.JSON(http.StatusAccepted, gin.H{
			"message":  "批量余额查询任务已添加到队列",
			"task_ids": taskIDs,
			"count":    len(tasks),
		})
	} else {
		c.JSON(http.StatusBadRequest, gin.H{"error": "必须提供 api_key_id 或 api_key_ids"})
	}
}



// GetHistory 获取余额历史记录
func (h *BalanceHandler) GetHistory(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid ID"})
		return
	}

	history, err := h.service.GetHistory(uint(id))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, history)
}


