export interface APIKey {
  id: number
  name: string
  provider: string
  api_key: string
  description?: string
  created_at: number  // Unix时间戳(毫秒)
  updated_at: number  // Unix时间戳(毫秒)

  // 最新测试信息
  last_test_status?: boolean
  last_test_time?: number  // Unix时间戳(毫秒)
  last_response_time?: number
  last_test_error?: string

  // 最新余额信息
  total_balance?: number
  grant_balance?: number
  paid_balance?: number
  last_balance_check?: number  // Unix时间戳(毫秒)
}

// 提供商相关类型
export interface Provider {
  name: string
  code: string
  color: string
  currency: string
  api_endpoint: string
  description: string
  support_balance: boolean
  balance_endpoint?: string
  support_chat: boolean
  chat_endpoint?: string
  chat_model?: string
}

// 任务相关类型
export interface Task {
  id: number
  type: 'test' | 'balance'
  status: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled'
  priority: number
  payload: string
  worker_id?: string
  started_at?: string
  completed_at?: string
  result?: string
  error_msg?: string
  retry_count: number
  max_retries: number
  next_retry_at?: string
  created_at: string
  updated_at: string
}

export interface QueueStats {
  pending: number
  running: number
  completed: number
  failed: number
  cancelled: number
}
