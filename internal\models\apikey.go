package models

import (
	"encoding/json"
	"time"
)

// APIKey 表示一个API密钥
type APIKey struct {
	ID          uint      `json:"id" gorm:"primary_key"`
	Name        string    `json:"name" gorm:"not null"`
	Provider    string    `json:"provider" gorm:"not null"` // openai, siliconflow等
	APIKey      string    `json:"api_key" gorm:"not null"`
	Description string    `json:"description"`

	// 最新测试信息
	LastTestStatus   *bool     `json:"last_test_status"`    // true表示成功, false表示失败, nil表示未测试
	LastTestTime     *time.Time `json:"last_test_time"`     // 最后测试时间
	LastResponseTime int       `json:"last_response_time"`  // 最后响应时间(毫秒)
	LastTestError    string    `json:"last_test_error"`     // 最后测试错误信息

	// 最新余额信息
	TotalBalance     float64    `json:"total_balance"`      // 总余额
	GrantBalance     float64    `json:"grant_balance"`      // 赠送余额
	PaidBalance      float64    `json:"paid_balance"`       // 充值余额
	LastBalanceCheck *time.Time `json:"last_balance_check"` // 最后余额检查时间

	CreatedAt   time.Time `json:"-"`
	UpdatedAt   time.Time `json:"-"`
}

// MarshalJSON 自定义JSON序列化，将时间转换为时间戳
func (a APIKey) MarshalJSON() ([]byte, error) {
	type Alias APIKey

	// 创建一个匿名结构体用于序列化
	return json.Marshal(&struct {
		*Alias
		LastTestTime     *int64 `json:"last_test_time"`
		LastBalanceCheck *int64 `json:"last_balance_check"`
		CreatedAt        int64  `json:"created_at"`
		UpdatedAt        int64  `json:"updated_at"`
	}{
		Alias:     (*Alias)(&a),
		CreatedAt: a.CreatedAt.UnixMilli(),
		UpdatedAt: a.UpdatedAt.UnixMilli(),
		LastTestTime: func() *int64 {
			if a.LastTestTime != nil {
				ts := a.LastTestTime.UnixMilli()
				return &ts
			}
			return nil
		}(),
		LastBalanceCheck: func() *int64 {
			if a.LastBalanceCheck != nil {
				ts := a.LastBalanceCheck.UnixMilli()
				return &ts
			}
			return nil
		}(),
	})
}



// TestResult 表示测试结果
type TestResult struct {
	ID           uint      `json:"id" gorm:"primary_key"`
	APIKeyID     uint      `json:"api_key_id"`
	TestType     string    `json:"test_type"`     // connectivity, chat, balance
	Status       string    `json:"status"`        // success, failed
	ResponseTime int       `json:"response_time"` // 毫秒
	ErrorMessage string    `json:"error_message"`
	CreatedAt    time.Time `json:"created_at"`
	
	APIKey APIKey `json:"api_key" gorm:"foreignkey:APIKeyID"`
}

// BalanceRecord 表示余额记录
type BalanceRecord struct {
	ID             uint      `json:"id" gorm:"primary_key"`
	APIKeyID       uint      `json:"api_key_id"`
	TotalBalance   float64   `json:"total_balance"`   // 总余额
	GrantBalance   float64   `json:"grant_balance"`   // 赠送余额
	PaidBalance    float64   `json:"paid_balance"`    // 充值余额
	Currency       string    `json:"currency" gorm:"default:'USD'"`
	CheckedAt      time.Time `json:"checked_at"`
	CreatedAt      time.Time `json:"created_at"`

	APIKey APIKey `json:"api_key" gorm:"foreignkey:APIKeyID"`
}
